# 密码加密指南

## 概述

本项目使用Jasypt库来加密配置文件中的敏感信息，如数据库密码。这样可以避免明文密码出现在配置文件中，提高安全性。

## 如何加密密码

1. 使用`JasyptEncryptorUtil`类来加密密码：

```java
// 运行JasyptEncryptorUtil类的main方法
// 默认使用"cloud"作为加密密钥
String encryptedPassword = JasyptEncryptorUtil.encrypt("cloud", "你的密码");
```

2. 在配置文件中使用加密后的密码，格式为：`ENC(加密后的密文)`

```yaml
spring:
  datasource:
    password: ENC(9/LV1vZbZOXYhWuVoXnYRlTWqWNiPTrH)
```

## 运行应用程序

运行应用程序时，需要提供加密密钥。有两种方式：

1. 使用环境变量：

```bash
export JASYPT_ENCRYPTOR_PASSWORD=cloud
java -jar target/paas-connect-0.0.1-SNAPSHOT.jar
```

2. 使用启动脚本：

```bash
./scripts/start-with-jasypt.sh
```

## 配置说明

Jasypt配置位于`application.yml`文件中：

```yaml
jasypt:
  encryptor:
    password: ${JASYPT_ENCRYPTOR_PASSWORD:cloud}  # 从环境变量获取密钥，默认为cloud
    algorithm: PBEWithMD5AndDES                   # 加密算法
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    bean: jasyptStringEncryptor                   # 自定义加密器Bean名称
```

## 安全建议

1. 不要在代码仓库中存储真实的加密密钥
2. 在生产环境中，通过环境变量或外部配置提供加密密钥
3. 定期更换加密密钥和密码
4. 限制配置文件的访问权限 