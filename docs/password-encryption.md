# 密码加密安全指南

本项目使用 Jasypt Spring Boot 进行数据库密码加密，以提高安全性。本文档提供了安全配置和最佳实践指南。

## 密码加密原理

1. 使用 Jasypt 库进行对称加密
2. 在配置文件中使用 `ENC()` 格式包裹加密后的密码
3. 通过 Jasypt Spring Boot 自动解密密码
4. 使用环境变量传递解密密钥，避免硬编码

## 当前加密配置

项目使用了以下加密配置：

```yaml
jasypt:
  encryptor:
    password: ${JASYPT_ENCRYPTOR_PASSWORD:cloud}
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
```

这些配置提供了：
- 标准加密算法 (PBEWithMD5AndDES)，确保最大兼容性
- 从环境变量获取密钥，默认值为 "cloud"
- 使用 NoIvGenerator 确保兼容性

## 如何生成加密密码

项目提供了两种加密工具：

### 1. 使用 JasyptEncryptorUtil 类

这是一个加密工具类，使用标准加密算法：

```bash
# 生成安全随机密钥 (推荐用于生产环境)
java -cp target/classes com.example.paasconnect.util.JasyptEncryptorUtil generateKey 32

# 使用密钥加密密码
java -cp target/classes com.example.paasconnect.util.JasyptEncryptorUtil encrypt <密钥> <明文密码>
```

### 2. 使用脚本工具

项目提供了便捷的脚本工具：

```bash
# 给予脚本执行权限
chmod +x scripts/encrypt-password.sh

# 加密密码
./scripts/encrypt-password.sh <明文密码> [密钥]
```

## 安全部署指南

### 1. 使用环境变量传递密钥

**开发环境**：
```bash
export JASYPT_ENCRYPTOR_PASSWORD="开发环境密钥"
java -jar app.jar
```

**测试环境**：
```bash
export JASYPT_ENCRYPTOR_PASSWORD="测试环境密钥"
java -jar app.jar
```

**生产环境**：
```bash
export JASYPT_ENCRYPTOR_PASSWORD="生产环境密钥"
java -jar app.jar
```

### 2. 使用安全启动脚本

项目提供了安全启动脚本 `scripts/start-secure.sh`，可以安全地传递密钥并在启动后清除环境变量。

### 3. 密钥管理最佳实践

- **不同环境使用不同密钥**：开发、测试和生产环境应使用不同的加密密钥
- **定期轮换密钥**：定期更换加密密钥和数据库密码
- **限制密钥访问**：只有需要启动应用的人员才能访问密钥
- **安全存储密钥**：考虑使用密钥管理服务 (如 HashiCorp Vault, AWS KMS) 存储密钥
- **审计和监控**：记录谁在何时访问了密钥

## 安全检查清单

- [ ] 确保配置文件中没有硬编码密钥
- [ ] 确保使用环境变量传递密钥
- [ ] 确保不同环境使用不同密钥
- [ ] 确保定期轮换密钥和密码
- [ ] 确保限制对密钥的访问
- [ ] 确保密钥不会被记录到日志中 