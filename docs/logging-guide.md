# 日志配置说明

本项目使用 Log4j2 作为日志实现，支持按天滚动日志文件，并提供了日志查询和管理功能。

## 1. 日志配置

### 1.1 依赖配置

项目使用 Spring Boot 的 log4j2-starter 依赖：

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-log4j2</artifactId>
</dependency>
```

同时排除了 Spring Boot 默认的 Logback 依赖：

```xml
<exclusion>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-logging</artifactId>
</exclusion>
```

### 1.2 日志级别

日志级别设置为 INFO，可在 application.yml 中配置：

```yaml
logging:
  config: classpath:log4j2-spring.xml
  level:
    root: info
    com.example.paasconnect: info
```

### 1.3 日志文件配置

日志文件配置在 log4j2-spring.xml 中，支持自适应日志目录：

- **生产环境**：使用 `/app/alarm_rule_8081/logs` 目录
- **开发环境**：如果无法访问上述目录，则使用 `${user.home}/logs/alarm_rule` 目录

日志文件包括：
- 主日志文件：`${LOG_HOME}/alarm.log`
- 按天滚动的日志文件：`${LOG_HOME}/alarm-yyyy-MM-dd.log`
- 错误日志文件：`${LOG_HOME}/error.log`
- 按天滚动的错误日志文件：`${LOG_HOME}/error-yyyy-MM-dd.log`

## 2. 日志管理功能

### 2.1 日志工具类

项目提供了 `LogUtil` 工具类，用于日志文件管理：

- 获取日志文件路径
- 读取日志文件内容
- 清理过期日志文件
- 自动选择适合当前环境的日志目录

### 2.2 日志清理

系统会自动清理超过 30 天的日志文件，清理任务在每天凌晨 2 点执行。

可以通过 `LogCleanupTask` 类修改日志保留天数：

```java
private static final int LOG_RETENTION_DAYS = 30; // 日志保留天数
```

### 2.3 日志查询 API

系统提供了以下 REST API 用于查询和管理日志：

- `GET /api/logs/latest?maxLines=1000` - 获取最新的日志内容
- `GET /api/logs/by-date?date=yyyy-MM-dd&maxLines=1000` - 获取指定日期的日志内容
- `GET /api/logs/list` - 获取可用的日志文件列表
- `GET /api/logs/cleanup?days=30` - 手动触发日志清理

## 3. 日志格式

日志使用以下格式记录：

```
yyyy-MM-dd HH:mm:ss.SSS [线程名] 日志级别 类名 - 日志内容
```

示例：

```
2023-07-20 14:30:45.123 [main] INFO com.example.paasconnect.PaasConnectApplication - 应用启动成功
```

## 4. 最佳实践

### 4.1 日志级别使用建议

- ERROR：系统错误，影响功能的异常情况
- WARN：警告信息，不影响功能但需要注意的情况
- INFO：重要业务流程，系统状态变化等信息
- DEBUG：详细调试信息（生产环境不开启）
- TRACE：更详细的调试信息（生产环境不开启）

### 4.2 日志记录建议

- 使用占位符 `{}` 而不是字符串拼接
- 记录关键业务流程的开始和结束
- 记录异常信息时包含异常堆栈
- 不要记录敏感信息（如密码、token）

### 4.3 日志目录

系统会自动选择适合当前环境的日志目录：

- **生产环境**：`/app/alarm_rule_8081/logs`
- **开发环境**：`${user.home}/logs/alarm_rule`

如果需要自定义日志目录，可以在启动应用时设置系统属性：

```bash
java -Dlog.home=/path/to/logs -jar app.jar
```

或者在代码中设置：

```java
System.setProperty("log.home", "/path/to/logs");
``` 