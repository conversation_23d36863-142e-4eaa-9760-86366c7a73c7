package com.example.paasconnect.config;

import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Jasypt 配置测试类
 * 用于验证加密配置是否正确
 */
@SpringBootTest
public class JasyptConfigTest {

    /**
     * 测试加密和解密
     */
    @Test
    public void testEncryptAndDecrypt() {
        // 创建与应用程序相同配置的加密器
        StringEncryptor encryptor = createEncryptor("cloud");
        
        // 开发环境密码
        String devPassword = "B6.bamcxkf20220824";
        String devEncrypted = encryptor.encrypt(devPassword);
        System.out.println("开发环境密码加密结果: ENC(" + devEncrypted + ")");
        String devDecrypted = encryptor.decrypt(devEncrypted);
        assertEquals(devPassword, devDecrypted, "开发环境密码加密解密不一致");
        
        // 测试环境密码
        String testPassword = "B6.sitech321";
        String testEncrypted = encryptor.encrypt(testPassword);
        System.out.println("测试环境密码加密结果: ENC(" + testEncrypted + ")");
        String testDecrypted = encryptor.decrypt(testEncrypted);
        assertEquals(testPassword, testDecrypted, "测试环境密码加密解密不一致");
        
        // 验证配置文件中的密文是否能正确解密
        String devConfigEncrypted = "9/LV1vZbZOXYhWuVoXnYRlTWqWNiPTrH";
        String testConfigEncrypted = "UvQdgPQ2Yk4Wz3Qk6ww6/Mzg+Ot3Tg8U";
        
        try {
            String devConfigDecrypted = encryptor.decrypt(devConfigEncrypted);
            System.out.println("开发环境配置密文解密结果: " + devConfigDecrypted);
            assertEquals(devPassword, devConfigDecrypted, "开发环境配置密文解密不正确");
            
            String testConfigDecrypted = encryptor.decrypt(testConfigEncrypted);
            System.out.println("测试环境配置密文解密结果: " + testConfigDecrypted);
            assertEquals(testPassword, testConfigDecrypted, "测试环境配置密文解密不正确");
            
            System.out.println("验证通过：配置文件中的密文可以正确解密！");
        } catch (Exception e) {
            System.err.println("解密失败: " + e.getMessage());
            throw e;
        }
    }
    
    /**
     * 创建与应用程序相同配置的加密器
     */
    private StringEncryptor createEncryptor(String password) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword(password);
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.NoIvGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        return encryptor;
    }
} 