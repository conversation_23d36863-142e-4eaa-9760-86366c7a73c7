server:
  port: 8081

spring:
  profiles:
    active: dev
  application:
    name: AlarmRule
jasypt:
  encryptor:
    password: cloud
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator

# 日志配置
logging:
  config: classpath:log4j2-spring.xml
  level:
    root: info
    com.example.paasconnect: info

alarm:
  yunying:
    mobile: 15701574012
  internal:
    phones: 15510201092

kafka:
  KAFKA_BROKER: 132.91.175.40:9092,132.91.175.41:9092,132.91.175.42:9092
  KAFKA_TOPIC: metrics