<?xml version="1.0" encoding="UTF-8"?>
<configuration status="WARN" monitorInterval="30">
    <properties>
        <!-- 直接指定日志目录 -->
        <property name="LOG_HOME">/app/yxjk_alarm_8081/logs/alarm_rule</property>
        <property name="FILE_NAME">alarm</property>
    </properties>
    
    <appenders>
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n" />
        </Console>
        
        <!-- 按天滚动的日志文件 -->
        <RollingFile name="RollingFileInfo" fileName="${LOG_HOME}/${FILE_NAME}.log"
                     filePattern="${LOG_HOME}/${FILE_NAME}-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n" />
            <Policies>
                <!-- 每天滚动一次 -->
                <TimeBasedTriggeringPolicy interval="1" modulate="true" />
                <!-- 日志文件大小超过 500MB 时也滚动 -->
                <SizeBasedTriggeringPolicy size="500MB" />
            </Policies>
            <!-- 保留最近 30 天的日志文件 -->
            <DefaultRolloverStrategy max="180">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="${FILE_NAME}-*.log" />
                    <IfLastModified age="30d" />
                </Delete>
            </DefaultRolloverStrategy>
            <!-- 只记录 INFO 及以上级别的日志 -->
            <Filters>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY" />
            </Filters>
        </RollingFile>
        
        <!-- 错误日志单独记录 -->
        <RollingFile name="RollingFileError" fileName="${LOG_HOME}/error.log"
                     filePattern="${LOG_HOME}/error-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n" />
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true" />
                <SizeBasedTriggeringPolicy size="100MB" />
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="error-*.log" />
                    <IfLastModified age="30d" />
                </Delete>
            </DefaultRolloverStrategy>
            <!-- 只记录 ERROR 级别的日志 -->
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY" />
            </Filters>
        </RollingFile>
        
        <!-- AutoKafkaProducer 单独日志文件 -->
        <RollingFile name="KafkaProducerLog" fileName="${LOG_HOME}/kafka-producer.log"
                     filePattern="${LOG_HOME}/kafka-producer-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n" />
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true" />
                <SizeBasedTriggeringPolicy size="200MB" />
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="kafka-producer-*.log" />
                    <IfLastModified age="30d" />
                </Delete>
            </DefaultRolloverStrategy>
            <Filters>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY" />
            </Filters>
        </RollingFile>
    </appenders>
    
    <loggers>
        <!-- AutoKafkaProducer 单独配置 -->
        <logger name="com.example.paasconnect.task.AutoKafkaProducer" level="INFO" additivity="false">
            <appender-ref ref="KafkaProducerLog" />
            <appender-ref ref="Console" />
        </logger>
        
        <!-- 框架日志级别 -->
        <logger name="org.springframework" level="INFO" />
        <logger name="org.mybatis" level="INFO" />
        <logger name="org.apache.kafka" level="INFO" />
        <logger name="com.alibaba.druid" level="INFO" />
        
        <!-- 应用日志级别 -->
        <logger name="com.example.paasconnect" level="INFO" />
        
        <!-- 根日志级别 -->
        <root level="INFO">
            <appender-ref ref="Console" />
            <appender-ref ref="RollingFileInfo" />
            <appender-ref ref="RollingFileError" />
        </root>
    </loggers>
</configuration>
