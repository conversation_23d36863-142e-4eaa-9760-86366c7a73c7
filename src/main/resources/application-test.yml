paas:
  mysql:
    user: unionmon
    pwd: KuBQTwD+/C6c3xYAAvqOENhYCk+EpHTR8bqBDWHOtWpl71Yo4LTHZbcjli6HtPfGlYzU0jYPRaDX6uDquf//lw==

spring:
  datasource:
    url: *****************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: cloud
    password: E<PERSON>(UvQdgPQ2Yk4Wz3Qk6ww6/Mzg+Ot3Tg8U)

alarm:
  weixinUrl: http://127.0.0.1:8000/home/<USER>
  duanxinUrl: http://127.0.0.1:8000/home/<USER>