<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.paasconnect.mapper.WebServiceTaskresultMapper">
    <!--获取最新的成功数据-->
    <select id="getSuccessData" parameterType="String" resultType="com.example.paasconnect.entity.WebServiceTaskresult">
        SELECT
            id,taskId,taskFileId,startTime,endTime,time,status,deviceId,reason
        FROM
            webservice_taskresult
        WHERE 1=1
            <if test="taskFileId!= null and taskFileId!= ''">
                AND  taskFileId = #{taskFileId}
            </if>
          AND `status` = '1' AND endTime > #{startTime}
        ORDER BY
            `endTime` DESC
    </select>
    <select id="getTaskData" parameterType="String" resultType="com.example.paasconnect.entity.WebServiceTaskresult">
        SELECT
        id,taskId,taskFileId,startTime,endTime,time,status,deviceId,reason
        FROM
        webservice_taskresult
        WHERE 1=1
        <if test="taskFileId!= null and taskFileId!= ''">
            AND  taskFileId = #{taskFileId}
        </if>
            AND endTime > #{startTime}
        <if test="endTime!= null and endTime!= ''">
            AND endTime &lt; #{endTime}
        </if>
        ORDER BY
        `endTime` DESC
    </select>
    <select id="getTaskDataBydeviceId" parameterType="String" resultType="com.example.paasconnect.entity.WebServiceTaskresult">
        SELECT
        id,taskId,taskFileId,startTime,endTime,time,status,deviceId,reason
        FROM
        webservice_taskresult
        WHERE 1=1
        <if test="deviceId!= null and deviceId!= ''">
            AND  deviceId = #{deviceId}
        </if>
             AND endTime > #{startTime}
          <if test="endTime!= null and endTime!= ''">
            AND endTime &lt; #{endTime}
        </if>
        ORDER BY
        `endTime` DESC
    </select>
<!--  按天统计数据, 推送到监控 kafka-->
    <select id="getTaskDataByTaskId" parameterType="String" resultType="Map">
        SELECT
        COUNT(*) AS totalDetections,  -- 总探测次数
        SUM(CASE WHEN `STATUS` = '1' THEN 1 ELSE 0 END) AS successCount,  -- 成功次数
        ROUND(SUM(CASE WHEN `STATUS` = '1' THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) AS successRate,  -- 成功率
        MAX(endTime) AS latestDetectionTime,  -- 最新探测时间
        MAX(CASE WHEN endTime = (
        SELECT MAX(endTime)
        FROM webservice_taskresult
        WHERE taskFileId = #{taskFileId}
        AND DATE(endTime) = CURDATE()
        ) THEN `STATUS` END) AS latestDetectionStatus,  -- 最新探测状态
        MAX(CASE WHEN endTime = (SELECT MAX(endTime)
        FROM webservice_taskresult
        WHERE taskFileId = #{taskFileId}
        AND DATE(endTime) = CURDATE())
        THEN reason END) AS latestDetectionReason  -- 最新探测原因
        FROM
        webservice_taskresult
        WHERE
        taskFileId = #{taskFileId}
        AND DATE(endTime) = CURDATE()  <!-- 只选择当天的数据 -->
    </select>
    <!-- 按小时统计每个业务的探测次数、失败数、成功率，展示业务名称，按探测次数降序排序 -->
    <select id="getHourlyStats" resultType="Map">
        SELECT
            t.taskFileId,
            d.task_file AS name,
            DATE_FORMAT(t.endTime, '%Y-%m-%d %H:00:00') AS hour,
            COUNT(*) AS total_count,
            SUM(CASE WHEN t.status = '0' THEN 1 ELSE 0 END) AS fail_count,
            ROUND(100.0 * SUM(CASE WHEN t.status = '1' THEN 1 ELSE 0 END) / COUNT(*), 2) AS success_rate
        FROM
            webservice_taskresult t
            LEFT JOIN webService_detectionunitid d ON t.taskFileId = d.uuitId
        WHERE
            t.endTime &gt;= #{startTime}
            AND t.endTime &lt;= #{endTime}
            AND t.taskFileId IS NOT NULL
        GROUP BY
            t.taskFileId, name, hour
        ORDER BY
            total_count DESC
    </select>
    <select id="getTotalStats" resultType="Map">
        SELECT
            t.taskFileId,
            d.task_file AS name,
            COUNT(*) AS total_count,
            SUM(CASE WHEN t.status = '0' THEN 1 ELSE 0 END) AS fail_count,
            ROUND(100.0 * SUM(CASE WHEN t.status = '1' THEN 1 ELSE 0 END) / COUNT(*), 2) AS success_rate
        FROM
            webservice_taskresult t
            LEFT JOIN webService_detectionunitid d ON t.taskFileId = d.uuitId
        WHERE
            t.endTime &gt;= #{startTime}
            AND t.endTime &lt;= #{endTime}
            AND t.taskFileId IS NOT NULL
        GROUP BY
            t.taskFileId, name
        ORDER BY
            total_count DESC
    </select>
    <select id="gettbLogPnmsPerformanceByID" resultType="Map">
        SELECT unit_id, cll_time_str
        FROM tb_log_pnms_performance
        GROUP BY unit_id
    </select>

 


</mapper>

