<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.paasconnect.mapper.WebServiceDetectionunitidMapper">
    <select id="getUnitId" parameterType="String" resultType="com.example.paasconnect.entity.WebServiceDetectionunitid">
        SELECT
            id,
            task_file AS taskFile,
            uuitId AS uuitId,
            task_type AS taskType
         FROM webService_detectionunitid
        <if test="detectionUnitId!= null and detectionUnitId!= ''">
            WHERE uuitId = #{detectionUnitId}
        </if>
    </select>
    <select id="getAllUnitId" resultType="com.example.paasconnect.entity.WebServiceDetectionunitid">
        SELECT
            p.id AS id,
            p.NAME AS taskFile,
            w.uuitId AS uuitId
        FROM
            webservice_detectevents p
                JOIN webservice_detectionunitid w ON w.uuitId = p.id
                LEFT JOIN ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY taskFileId ORDER BY endTime DESC ) AS rn FROM webservice_taskresult ) t ON w.uuitId = t.taskFileId
                AND t.rn = 1
        WHERE t.STATUS = '0'
          and p.`enable` = '1'
    </select>

    <select id="getDevicesList" resultType="com.example.paasconnect.entity.WebServiceDetectionunitid">
        SELECT deviceId AS uuitId,`name` as taskFile FROM `g41_szh_bamdb`.`webservice_webdeviceinfo` WHERE `status` = '1' and monitorType = '1'
    </select>

    <select id="getPcUnitId" resultType="com.example.paasconnect.entity.WebServiceDetectionunitid">
        SELECT
        id,
        task_file AS taskFile,
        uuitId AS uuitId,
        task_type AS taskType
        FROM pcservice_detectionunitid
    </select>
     <!--获取近5分钟失败的任务结果数据-->
    <select id="getFailedTasksInLast5Minutes" resultType="com.example.paasconnect.entity.WebServiceTaskresult">
        SELECT
            id,taskId,taskFileId,startTime,endTime,time,status,deviceId,reason
        FROM
            webservice_taskresult
        WHERE
            `status` = '0'
            AND endTime >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ORDER BY
            taskFileId, endTime DESC
    </select>
</mapper>

