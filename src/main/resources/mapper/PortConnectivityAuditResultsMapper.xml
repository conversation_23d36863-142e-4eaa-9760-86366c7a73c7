<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.paasconnect.mapper.PortConnectivityAuditResultsMapper">
    <!-- 根据时间查询日志 -->
    <select id="seleteByTime" parameterType="map" resultType="com.example.paasconnect.entity.PortConnectivityAuditResults">
        SELECT name,ip_address,final_status,failure_count,call_time,task_id,unit_id,create_time,last_failure_time
        FROM port_connectivity_audit_results
        WHERE create_time like  CONCAT(#{createTime},'%')
    </select>
    <select id="gettbLogPnmsPerformanceByID" parameterType="map" resultType="com.example.paasconnect.entity.PortConnectivityAuditResults">
        SELECT cicode as auditId
        FROM `g41_szh_cmdbdb`.`t_res_target_config`
        WHERE `res_type` = '4'
          AND (res_sub_class = #{name} OR res_name = #{name})
    </select>
</mapper>

