<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.paasconnect.mapper.WebServiceMonitoringconfigurationMapper">
    <resultMap id="WebServiceMonitoringconfiguration" type="com.example.paasconnect.entity.WebServiceMonitoringconfiguration">
        <result column="unitId" property="id"/>
        <result column="convergeId" property="convergeId"/>
        <result column="name" property="remark"/>
    </resultMap>
    <!--获取告警接收人-->
    <select id="queryPhone" resultType="com.example.paasconnect.entity.WebServiceMonitoringconfiguration">
        SELECT id,unit_id,alarm_type,alarm_phone,alarm_receiver,alarm_power,converge_id FROM webService_monitoringconfiguration WHERE alarm_power = 'on' and  unit_id = #{unitId}
    </select>
    <select id="getsendAlarmDate" resultType="com.example.paasconnect.entity.WebServiceMonitoringconfiguration">
        SELECT
            t1.unit_id AS unitId,
            t3.id AS convergeId,
            t3.NAME AS remark
        FROM
            tb_log_send_alarm t1
                LEFT JOIN webservice_monitoringconfiguration t2 ON t2.unit_id = t1.unit_id
                LEFT JOIN tb_log_alarm_converge t3 ON t2.converge_id = t3.id
        GROUP BY
            t1.unit_id
    </select>
</mapper>

