package com.example.service;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import java.util.Properties;
import java.util.concurrent.ExecutionException;

public class KafkaProducerStable {
    public static void main(String[] args) {
        // Kafka 集群配置
        String bootstrapServers = "132.91.175.40:9092,132.91.175.41:9092,132.91.175.42:9092"; // 替换为你的 Kafka broker 地址
        String topic = "metrics"; // 替换为你的 Kafka 主题
        String username = "user11"; // 替换为你的用户名
        String password = "2xFm8wAOyQ"; // 替换为你的密码

        // 配置生产者属性
        Properties props = new Properties();
        props.put("bootstrap.servers", bootstrapServers); // Kafka 集群地址
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer"); // Key序列化
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer"); // Value序列化
        props.put("acks", "all"); // 确保数据完全同步到所有副本
        props.put("retries", 5); // 重试次数，增加稳定性
        props.put("enable.idempotence", "true"); // 开启幂等性，防止重复发送
        props.put("linger.ms", 5); // 延迟时间，减少请求数，提高吞吐量
        props.put("batch.size", 16384); // 批量发送的大小（16KB）
        props.put("compression.type", "snappy"); // 压缩消息，减少网络传输开销


        props.put("security.protocol", "SASL_PLAINTEXT"); // 使用 SASL_PLAINTEXT 协议
        props.put("sasl.mechanism", "PLAIN"); // 使用 PLAIN 认证机制
        props.put("sasl.jaas.config", String.format(
                "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";",
                username, password
        ));
        // 创建 Kafka 生产者
        KafkaProducer<String, String> producer = new KafkaProducer<>(props);

        try {
            // 模拟发送 100 条数据
            for (int i = 1; i <= 100; i++) {
                String key = "key" + i;
                String value = "message" + i;

                // 创建消息
                ProducerRecord<String, String> record = new ProducerRecord<>(topic, key, value);

                // 发送消息并等待结果
                try {
                    RecordMetadata metadata = producer.send(record).get(); // 同步发送，确保发送成功
                    System.out.printf("Message sent: Topic=%s, Partition=%d, Offset=%d, Key=%s, Value=%s%n",
                            metadata.topic(), metadata.partition(), metadata.offset(), key, value);
                } catch (ExecutionException | InterruptedException e) {
                    System.err.println("Error sending message: " + e.getMessage());
                }
            }
        } finally {
            // 关闭生产者
            producer.close();
        }
    }
}