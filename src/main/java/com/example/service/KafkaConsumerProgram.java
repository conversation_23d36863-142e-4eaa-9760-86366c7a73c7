package com.example.service;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;

import java.time.Duration;
import java.util.Collections;
import java.util.Properties;

public class KafkaConsumerProgram {
    public static void main(String[] args) {
        // Kafka 配置
        String bootstrapServers = "132.91.175.40:9092,132.91.175.41:9092,132.91.175.42:9092";// 替换为你的 Kafka broker 地址
        String topic = "metrics"; // 替换为你的 Kafka 主题
        String groupId = "pnms-group"; // 消费者组 ID

        String username = "user11"; // 替换为你的用户名
        String password = "2xFm8wAOyQ"; // 替换为你的密码

        // 配置消费者属性
        Properties props = new Properties();
        props.put("bootstrap.servers", bootstrapServers);
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("group.id", groupId);
        props.put("auto.offset.reset", "earliest"); // 从最早的消息开始消费


        // 配置 SASL/PLAIN 认证
        props.put("security.protocol", "SASL_PLAINTEXT"); // 使用 SASL_PLAINTEXT 协议
        props.put("sasl.mechanism", "PLAIN"); // 使用 PLAIN 认证机制
        props.put("sasl.jaas.config", String.format(
                "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";",
                username, password
        ));

        // 创建 Kafka 消费者
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);

        // 订阅主题
        consumer.subscribe(Collections.singletonList(topic));

        try {
            while (true) {
                // 拉取消息
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                for (ConsumerRecord<String, String> record : records) {
                    System.out.printf("guojian——Consumed message: Topic=%s, Partition=%d, Offset=%d, Key=%s, Value=%s%n",
                            record.topic(), record.partition(), record.offset(), record.key(), record.value());
                }
            }
        } finally {
            consumer.close();
        }
    }
}