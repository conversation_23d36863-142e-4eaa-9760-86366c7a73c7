package com.example.service;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.Properties;

@Service
public class KafkaService {

    @Value("${kafka.KAFKA_BROKER}")
    private static String kafkaBroker = "10.211.55.5:9092";

    @Value("${kafka.KAFKA_TOPIC}")
    private static String kafkaTopic = "metrics";

    public void printKafkaConfig() {
        System.out.println("Kafka Broker: " + kafkaBroker);
        System.out.println("Kafka Topic: " + kafkaTopic);
    }


    // 创建 Kafka 生产者
    private static KafkaProducer<String, String> createKafkaProducer() {
        Properties props = new Properties();
        props.put("bootstrap.servers", kafkaBroker);
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        return new KafkaProducer<>(props);
    }

    // 查询沃易售探测监控数据
    private static ResultSet fetchUnprocessedResults() throws SQLException {

return null;
//        return statement.executeQuery();
    }

    // 更新数据库状态为已处理
    private static void updateStatusToProcessed(int id) throws SQLException {
//        Connection connection = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
//        String updateQuery = "UPDATE monitoring_results SET status = 1 WHERE id = ?";
//        PreparedStatement statement = connection.prepareStatement(updateQuery);
//        statement.setInt(1, id);
//        statement.executeUpdate();
//        connection.close();
    }

    // 发送数据到 Kafka
    private static void sendToKafka(KafkaProducer<String, String> producer, String topic, String message) {
        ProducerRecord<String, String> record = new ProducerRecord<>(topic, message);
        producer.send(record, (metadata, exception) -> {
            if (exception != null) {
                System.err.println("Error sending message to Kafka: " + exception.getMessage());
            } else {
                System.out.println("Message sent to Kafka: " + metadata.topic() + " - Partition: " + metadata.partition() + " - Offset: " + metadata.offset());
            }
        });
    }




//    public static void main(String[] args) {
//        // 创建 Kafka 生产者
//        KafkaProducer<String, String> producer = createKafkaProducer();
//
//        while (true) {
//            try {
//                // 查询未处理的监控数据
//                ResultSet results = fetchUnprocessedResults();
//
//                // 处理查询到的数据
////                while (results.next()) {
////                    int id = results.getInt("id");
////                    String resultData = results.getString("result_data");
//                    String resultData = "guojian-test-data";
//                    // 发送数据到 Kafka
//                    sendToKafka(producer, kafkaTopic, resultData);
//
//                    // 更新数据库状态为已处理
////                    updateStatusToProcessed(id);
////                }
//
//                // 等待 10 秒再执行下一轮
//                Thread.sleep(10000);
//
//            } catch (Exception e) {
//                e.printStackTrace();
//                try {
//                    // 如果出错，等待一段时间再重试
//                    Thread.sleep(10000);
//                } catch (InterruptedException ie) {
//                    ie.printStackTrace();
//                }
//            }
//        }
//    }
}