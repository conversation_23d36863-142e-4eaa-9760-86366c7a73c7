package com.example.service;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import java.util.Properties;

public class KafkaProducerProgram {
    public static void main(String[] args) {
        // Kafka 配置
        String bootstrapServers = "132.91.175.40:9092;132.91.175.41:9092;132.91.175.42:9092"; // 替换为你的 Kafka broker 地址
        String topic = "metrics"; // 替换为你的 Kafka 主题

        String username = "user11"; // 替换为你的用户名
        String password = "2xFm8wAOyQ"; // 替换为你的密码
        // 配置生产者属性
        Properties props = new Properties();
        props.put("bootstrap.servers", bootstrapServers);
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");

        // 创建 Kafka 生产者
        KafkaProducer<String, String> producer = new KafkaProducer<>(props);

        try {
            // 发送 10 条测试消息
            for (int i = 1; i <= 10; i++) {
                String key = "key" + i;
                String value = "message" + i;
                ProducerRecord<String, String> record = new ProducerRecord<>(topic, key, value);

                // 发送消息
                producer.send(record, (metadata, exception) -> {
                    if (exception != null) {
                        System.err.println("Error sending message: " + exception.getMessage());
                    } else {
                        System.out.println("Message sent: " + metadata.topic() + " | Partition: " + metadata.partition() + " | Offset: " + metadata.offset());
                    }
                });
            }
        } finally {
            // 关闭生产者
            producer.close();
        }
    }
}