package com.example.paasconnect.util;

import com.alibaba.druid.filter.config.ConfigTools;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
/**
 * @ClassName MD5
 * @Deacription TODO
 * @<PERSON> g<PERSON><PERSON><PERSON>
 * @Date 2022/3/22 15:06
 * @Version 1.0
 **/
public class MD5 {
    /**
     * MD5方法
     *
     * @param text 明文
     * @param key 密钥
     * @return 密文
     * @throws Exception
     */
    public static String md5(String text, String key) throws Exception {
        //加密后的字符串
        String encodeStr= DigestUtils.md5DigestAsHex("aaaa".getBytes(StandardCharsets.UTF_8));
//                md5Hex(text + key);
        System.out.println("MD5加密后的字符串为:encodeStr="+encodeStr);
        return encodeStr;
    }

    /**
     * MD5验证方法
     *
     * @param text 明文
     * @param key 密钥
     * @param md5 密文
     * @return true/false
     * @throws Exception
     */
    public static boolean verify(String text, String key, String md5) throws Exception {
        //根据传入的密钥进行验证
        String md5Text = md5(text, key);
        if(md5Text.equalsIgnoreCase(md5))
        {
            System.out.println("MD5验证通过");
            return true;
        }

        return false;
    }

//    public static void main(String[] args) {
//        String ss = "Unionmon123";
//        try {
//
//            ss = ConfigTools.encrypt("Unionmon123");
//
//            System.out.println(ss);
//
////        System.out.println(ConfigTools.encrypt("Bam%2020"));
////
////        System.out.println(ConfigTools
////                            .decrypt("RSC1D9uByzpL/abF00qC2xCMcm6d2mb36YoiAbLJ5LdPtWwUFu/t6SnE6FkRkztI2K9Fk4HWiySAeg1+wPLkvQ=="));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }


}
