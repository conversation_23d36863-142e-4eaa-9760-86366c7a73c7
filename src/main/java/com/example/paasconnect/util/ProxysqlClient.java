package com.example.paasconnect.util;

import com.alibaba.fastjson.JSONObject;
import com.example.paasconnect.entity.PaasVipBusiness;
import com.github.jasync.sql.db.Connection;
import com.github.jasync.sql.db.QueryResult;
import com.github.jasync.sql.db.ResultSet;
import com.github.jasync.sql.db.RowData;
import com.github.jasync.sql.db.mysql.MySQLConnectionBuilder;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * @title: ProxysqlClient
 * @Description:
 * <AUTHOR>
 * @date 2022/3/4 17:13
 */
public class ProxysqlClient {

    private final static Logger log = LoggerFactory.getLogger(ProxysqlClient.class);

    /**
     * mysql 默认连接的schema
     * proxysql 虽然没有这个schema，但并不会报错
     */
    public static final String DEFAULT_SCHEMA = "information_schema";
    private static final String EXECUTE_SQL_ERROR = "执行sql[%s]异常";
    private static final String EXECUTE_SQL_TIMEOUT = "执行sql[%s]超时";

    /**
     * 禁止用new实例化
     */
    private ProxysqlClient() {
    }

    /**
     * 执行proxysql语句，但不需要执行结果
     * 可以多条语句顺序执行，所有语句在同一个事务中执行
     * 注意：proxysql不支持事务，但并不会报错
     * 测试通过：mysql、proxysql
     *
     * @param ip          必填，服务IP
     * @param port        必填，服务端口
     * @param database    选填，数据库名称，默认 information_schema
     * @param user        必填，用户名
     * @param pass        必填，密码
     * @param sqlList     必填，待执行的sql列表
     * @param timeout     选填，超时时间，单位s，默认不设超时时间
     * @param transaction 是否开启事务
     * @return 成功or失败
     */
    public static boolean execute(String ip, Integer port, String database, String user, String pass, List<String> sqlList, Integer timeout, boolean transaction) {
        //参数检查
        if (StringUtils.isBlank(ip) || port == null || StringUtils.isBlank(user) || StringUtils.isBlank(pass)) {
            throw new ProxysqlException("参数错误：ip/port/user/pass不能为空");
        }

        //默认连接information_schema
        if (StringUtils.isBlank(database)) {
            database = DEFAULT_SCHEMA;
        }

        if (sqlList == null || sqlList.isEmpty()) {
            throw new ProxysqlException("执行sql为空");
        }

        Connection connection = MySQLConnectionBuilder.createConnectionPool(
                String.format("*****************************************", ip, port, database, user, pass)
        );

        CompletableFuture<QueryResult> result = null;
        if (transaction) {
            result = connection.inTransaction(c -> {
                CompletableFuture<QueryResult> future = null;
                for (String sql : sqlList) {
                    if (future == null) {
                        future = c.sendQuery(sql);
                    } else {
                        future = future.thenCompose(r -> c.sendQuery(sql));
                    }
                }
                return future;
            });
        } else {
            for (String sql : sqlList) {
                if (result == null) {
                    result = connection.sendQuery(sql);
                } else {
                    result = result.thenCompose(r -> connection.sendQuery(sql));
                }
            }
        }

        if (result == null) {
            log.error("执行sql失败！");
            return false;
        }

        try {
            if (timeout != null && timeout > 0) {
                result.get(timeout, TimeUnit.SECONDS);
            } else {
                result.get();
            }
        } catch (InterruptedException e) {
            log.error(String.format(EXECUTE_SQL_ERROR, StringUtils.join((Iterator) sqlList, ";")), e);
            Thread.currentThread().interrupt();
            throw new ProxysqlException(String.format(EXECUTE_SQL_ERROR, StringUtils.join((Iterator) sqlList, ";")) + ": InterruptedException");
        } catch (ExecutionException e) {
            log.error(String.format(EXECUTE_SQL_ERROR, StringUtils.join((Iterator) sqlList, ";")), e);
            throw new ProxysqlException(String.format(EXECUTE_SQL_ERROR, StringUtils.join((Iterator) sqlList, ";")) + ": " + e.getMessage());
        } catch (TimeoutException e) {
            log.error(String.format(EXECUTE_SQL_ERROR, StringUtils.join((Iterator) sqlList, ";")), e);
            throw new ProxysqlException(String.format(EXECUTE_SQL_TIMEOUT, StringUtils.join((Iterator) sqlList, ";")) + "：" + timeout + "s");
        }

        connection.disconnect();

        return true;
    }

    /**
     * 执行proxysql语句，返回执行结果
     *
     * @param ip        必填，服务IP
     * @param port      必填，服务端口
     * @param database  选填，数据库名称，默认 information_schema
     * @param user      必填，用户名
     * @param pass      必填，密码
     * @param sqlString 必填，待执行的sql
     * @param timeout   选填，超时时间，单位s，默认不设超时时间
     * @return List<Map> 所有字段名均为数据库原始字段名
     */
    public static List<Map<String, Object>> executeQuery(String ip, Integer port, String database, String user, String pass, String sqlString, Integer timeout) {
        //参数检查
        if (StringUtils.isBlank(ip) || port == null || StringUtils.isBlank(user) || StringUtils.isBlank(pass)) {
            throw new ProxysqlException("参数错误：ip/port/user/pass不能为空");
        }

        //默认连接information_schema
        if (StringUtils.isBlank(database)) {
            database = DEFAULT_SCHEMA;
        }

        if (StringUtils.isBlank(sqlString)) {
            throw new ProxysqlException("执行sql为空");
        }

        Connection connection = MySQLConnectionBuilder.createConnectionPool(
                String.format("*****************************************", ip, port, database, user, pass)
        );

        CompletableFuture<QueryResult> future = connection.sendQuery(sqlString);
        List<Map<String, Object>> resultList = new ArrayList<>();
        QueryResult queryResult;
        try {
            if (timeout == null || timeout <= 0) {
                queryResult = future.get();
            } else {
                queryResult = future.get(timeout, TimeUnit.SECONDS);
            }
            ResultSet resultSet = queryResult.getRows();
            List<String> names = resultSet.columnNames();

            for (RowData rowData : resultSet) {
                Map<String, Object> map = new HashMap<>(16);
                for (String name : names) {
                    map.put(name, rowData.get(name));
                }
                resultList.add(map);
            }
        } catch (InterruptedException e) {
            log.error(String.format(EXECUTE_SQL_ERROR, sqlString), e);
            Thread.currentThread().interrupt();
            throw new ProxysqlException(String.format(EXECUTE_SQL_ERROR, sqlString) + ": InterruptedException");
        } catch (ExecutionException e) {
            log.error(String.format(EXECUTE_SQL_ERROR, sqlString), e);
            throw new ProxysqlException(String.format(EXECUTE_SQL_ERROR, sqlString) + ": " + e.getMessage());
        } catch (TimeoutException e) {
            log.error(String.format(EXECUTE_SQL_ERROR, sqlString), e);
            throw new ProxysqlException(String.format(EXECUTE_SQL_TIMEOUT, sqlString) + "：" + timeout + "s");
        }finally {
            connection.disconnect();
        }
        return resultList;
    }
    public static JSONObject executeQuerySize(String ip, Integer port, String database, String user, String pass, String sqlString, Integer timeout,List<PaasVipBusiness> schemeList) {

        JSONObject jsonObject = new JSONObject();
        //参数检查
        if (StringUtils.isBlank(ip) || port == null || StringUtils.isBlank(user) || StringUtils.isBlank(pass)) {
            throw new ProxysqlException("参数错误：ip/port/user/pass不能为空");
        }
        //默认连接information_schema
        if (StringUtils.isBlank(database)) {
            database = DEFAULT_SCHEMA;
        }

        if (StringUtils.isBlank(sqlString)) {
            throw new ProxysqlException("执行sql为空");
        }

        Connection connection = MySQLConnectionBuilder.createConnectionPool(
                String.format("*****************************************", ip, port, database, user, pass)
        );

        CompletableFuture<QueryResult> future = connection.sendQuery(sqlString);

        QueryResult queryResult;

        try {
            if (timeout == null || timeout <= 0) {
                queryResult = future.get();
            } else {
                queryResult = future.get(timeout, TimeUnit.SECONDS);
            }
//            int num = queryResult.getRows().size();  //总数
//            log.info("总数：" + num);
            ResultSet resultSet = queryResult.getRows();
            List<String> names = resultSet.columnNames();

            if (schemeList.size()>0){
                for (int i = 0; i < schemeList.size(); i++) {
                    PaasVipBusiness paasVipBusiness = schemeList.get(i);
                    String scheme = paasVipBusiness.getDatabaseName();
                    int count = 0;
                    for (RowData rowData : resultSet) {
                        for (String name : names) {
                            if("SCHEMA".equals(name) || "schema".equals(name)){
                                if(scheme.equals(rowData.get(name))){
                                    count++;
                                }
                            }
                        }
                    }
                    jsonObject.put(scheme,count);
                }
            }
        } catch (InterruptedException e) {
            log.error(String.format(EXECUTE_SQL_ERROR, sqlString), e);
            Thread.currentThread().interrupt();
            throw new ProxysqlException(String.format(EXECUTE_SQL_ERROR, sqlString) + ": InterruptedException");
        } catch (ExecutionException e) {
            log.error(String.format(EXECUTE_SQL_ERROR, sqlString), e);
            throw new ProxysqlException(String.format(EXECUTE_SQL_ERROR, sqlString) + ": " + e.getMessage());
        } catch (TimeoutException e) {
            log.error(String.format(EXECUTE_SQL_ERROR, sqlString), e);
            throw new ProxysqlException(String.format(EXECUTE_SQL_TIMEOUT, sqlString) + "：" + timeout + "s");
        }finally {
            connection.disconnect();
        }
        return jsonObject;
    }

//    public static void main(String[] args) {
//        //select
////        log.info(JSON.toJSONString(executeQuery("**************", 3306, "bam60", "bam", "Bam%2020",
////                "show databases",
////                null)));
////        //ddl & batch execute
////        log.info(JSON.toJSONString(execute("localhost", 6032, "main", "root", "root",
////                Arrays.asList("INSERT INTO mysql_users(username,password,default_hostgroup) VALUES ('app','pass',2)", "load mysql user to runtime", "save mysql user to disk"),
////                null, false)));
//
//
////
////        log.info(JSON.toJSONString(executeQuery("***********", 9070, "d51_wyzx_mobdb", "unionmon", "Unionmon123",
////                "show @@connection",
////                30)));
//
////        {"schmeList":[{"businessName":"沃易售","databaseName":"d51_xxh_wysdb","id":"1","managePort":"9071","servicePort":"8071","vip":"************"},{"businessName":"沃易售开放平台","databaseName":"d51_xxh_appdevplatformdb","id":"2","managePort":"9071","servicePort":"8071","vip":"************"},{"businessName":"沃易售灰度环境","databaseName":"d51_xxh_appdevplatdb","id":"3","managePort":"9071","servicePort":"8071","vip":"************"},{"businessName":"智能排队","databaseName":"d51_xxh_znpddb","id":"4","managePort":"9071","servicePort":"8071","vip":"************"},{"businessName":"二区收入","databaseName":"d51_eq_erqudb","id":"5","managePort":"9071","servicePort":"8071","vip":"************"},{"businessName":"合作伙伴管理系统","databaseName":"d51_xxh_parterstestdb","id":"6","managePort":"9071","servicePort":"8071","vip":"************"},{"businessName":"粉丝中心二期","databaseName":"d51_xxh_bjwxbhdb","id":"7","managePort":"9071","servicePort":"8071","vip":"************"},{"businessName":"流程随心定生产环境","databaseName":"d51_xxh_itsxddb","id":"8","managePort":"9071","servicePort":"8071","vip":"************"},{"businessName":"OA云盘生产环境","databaseName":"d51_xxh_oaypdb","id":"9","managePort":"9071","servicePort":"8071","vip":"************"}],"vip":"************","ips":"***********","managePort":"9071"}
//
//        List<PaasVipBusiness> schmeList = new ArrayList<>();
//        PaasVipBusiness p = new PaasVipBusiness();
//        p.setDatabaseName("d51_xxh_wysdb");
//        PaasVipBusiness p1 = new PaasVipBusiness();
//        p1.setDatabaseName("d51_xxh_appdevplatformdb");
//        schmeList.add(p);
//
//        log.info(JSON.toJSONString(executeQuerySize("***********", 9071, "d51_xxh_wysdb", "unionmon", "Unionmon123",
//                "show @@connection",
//                30,schmeList)));
////        try {
////            Thread.sleep(1000);
////        } catch (InterruptedException e) {
////            e.printStackTrace();
////        }
////        log.info(JSON.toJSONString(executeQuerySize("***********", 9070, "d51_xxh_appdevplatdb", "unionmon", "Unionmon123",
////                "show @@connection",
////                30)));
//    }

}