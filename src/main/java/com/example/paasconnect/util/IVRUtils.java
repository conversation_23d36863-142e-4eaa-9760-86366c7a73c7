package com.example.paasconnect.util;

import com.alibaba.fastjson.JSONObject;
import com.example.paasconnect.exception.HttpClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Random;

/**
 * @Description: 短信发送工具类
 * @param: null
 * @return:
 * @Author: g<PERSON><PERSON><PERSON>
 * @Date 2023/9/22 14:15
 */
public class IVRUtils {
    private final static Logger log = LoggerFactory.getLogger(IVRUtils.class);
    public static String generateRandomNumber(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int digit = random.nextInt(10); // 生成0到9之间的随机数字
            sb.append(digit);
        }

        return sb.toString();
    }

    public static String sendIvrAlarm(String mobile, String content) {
        //生产入口
        String url = "http://*************:8000/api/chinaUnicom/bj11/intelligentMarketing/ivrcalltask/v1";
        String key = "2xp78W2A3w69hxsan905tfOnnD0i0AOO";

        //测试入口
//        String url = "http://**************:8000/api/chinaUnicom/bj11/intelligentMarketing/ivrcalltask/v1";
//        String key = "AAo3A7JtOmgE7rj4nWJ4zM2jBXFfN8Sm"; //测试

        //授权码 用的是贾梦园的账号q
        String app_id = "VEwVtF37AY";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss SSS");
        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyyMMddhhmmssSSS");
        long l = System.currentTimeMillis();
        String TIMESTAMP = dateFormat.format(l);
        String TRANS_ID = dateFormat1.format(l) + generateRandomNumber(6);
        String value = "APP_ID" + app_id + "TIMESTAMP" + TIMESTAMP + "TRANS_ID" + TRANS_ID + key;
        System.out.println("加密前：" + value);
        String token = MD5Encryption.encrypt(value);
        JSONObject UNI_BSS_HEAD = new JSONObject();
        UNI_BSS_HEAD.put("APP_ID", app_id);
        UNI_BSS_HEAD.put("TIMESTAMP", TIMESTAMP);
        UNI_BSS_HEAD.put("TRANS_ID", TRANS_ID);
        UNI_BSS_HEAD.put("TOKEN", token);
        //body 拼接
        String systemID = "YWJKPT";
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("YYYYMMDDhhmmss");
        System.out.println(dateFormat2.format(l));
        String uuid = dateFormat2.format(l) + systemID + generateRandomNumber(4);
        System.out.println(uuid);
//        String calledNo = "15510201092";
//        String explicitNo = "15701574012";
//        String msg1 = "告警测试，您收到一条业务监控的告警，请登录系统查看是否正常运行";
        String whetherFrequency = "0";//是否频控   0-呼叫频控数据    1-不呼叫频控数据
        String whetherBlack = "0";//是否黑名单 0-外呼全部用户 1-不呼叫服务免打扰用户 2-不呼叫营销免打扰用户 3-不呼叫所有免打扰用户（服务+营销）
        JSONObject IVRCALLTASK_REQ = new JSONObject();
        IVRCALLTASK_REQ.put("systemID", systemID);
        IVRCALLTASK_REQ.put("uuid", uuid);
//        IVRCALLTASK_REQ.put("calledNo", calledNo);
//        IVRCALLTASK_REQ.put("explicitNo", explicitNo);
//        IVRCALLTASK_REQ.put("msg1", msg1);
        IVRCALLTASK_REQ.put("calledNo", mobile);
        IVRCALLTASK_REQ.put("explicitNo", "85684000");
        IVRCALLTASK_REQ.put("msg1", content);
        IVRCALLTASK_REQ.put("whetherFrequency", whetherFrequency);
        IVRCALLTASK_REQ.put("whetherBlack", whetherBlack);
        IVRCALLTASK_REQ.put("messageContent", content);

        JSONObject UNI_BSS_BODY = new JSONObject();
        UNI_BSS_BODY.put("IVRCALLTASK_REQ", IVRCALLTASK_REQ);

        JSONObject MEDIA_INFO = new JSONObject();
        MEDIA_INFO.put("MEDIA_INFO", "");
        JSONObject obj = new JSONObject();
        obj.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
        obj.put("UNI_BSS_BODY", UNI_BSS_BODY);
        obj.put("UNI_BSS_ATTACHED", MEDIA_INFO);
        log.info("入参：" + obj.toJSONString());
        try {
            String post = HttpClientUtil.post(url, obj);
            log.info("出参：" + post);
            return post;
        } catch (HttpClientException e) {
            throw new RuntimeException(e);
        }
    }

//    public static void main(String[] args) {
//        //生产入口
//        String url = "http://*************:8000/api/chinaUnicom/bj11/intelligentMarketing/ivrcalltask/v1";
//        String key = "2xp78W2A3w69hxsan905tfOnnD0i0AOO";
//
//        //测试入口
////        String url = "http://**************:8000/api/chinaUnicom/bj11/intelligentMarketing/ivrcalltask/v1";
////        String key = "AAo3A7JtOmgE7rj4nWJ4zM2jBXFfN8Sm"; //测试
//
//        //授权码 用的是贾梦园的账号q
//        String app_id = "VEwVtF37AY";
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss SSS");
//        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyyMMddhhmmssSSS");
//        long l = System.currentTimeMillis();
//        String TIMESTAMP = dateFormat.format(l);
//        String TRANS_ID = dateFormat1.format(l) + generateRandomNumber(6);
//        String value = "APP_ID" + app_id + "TIMESTAMP" + TIMESTAMP + "TRANS_ID" + TRANS_ID + key;
//        System.out.println("加密前：" + value);
//        String token = MD5Encryption.encrypt(value);
//        JSONObject UNI_BSS_HEAD = new JSONObject();
//        UNI_BSS_HEAD.put("APP_ID", app_id);
//        UNI_BSS_HEAD.put("TIMESTAMP", TIMESTAMP);
//        UNI_BSS_HEAD.put("TRANS_ID", TRANS_ID);
//        UNI_BSS_HEAD.put("TOKEN", token);
//        //body 拼接
//        String systemID = "YWJKPT";
//        SimpleDateFormat dateFormat2 = new SimpleDateFormat("YYYYMMDDhhmmss");
//        System.out.println(dateFormat2.format(l));
//        String uuid = dateFormat2.format(l) + systemID + generateRandomNumber(4);
//        System.out.println(uuid);
//        String calledNo = "15510201092";
//        String explicitNo = "85684000";
//        String msg1 = "【统一业务监控-手机探测】[一般告警][企业微信][新沃推荐页面加载探测]任务名：北京联通-新沃推荐,探测结果:失败，失败原因：新沃推荐详情页面打开时间超过设定阈值，探测结束,探测时间：2025-01-22 09:49:15,耗时：65100毫秒 [业务负责人：董群吉]";
//        String whetherFrequency = "0";//是否频控   0-呼叫频控数据    1-不呼叫频控数据
//        String whetherBlack = "0";//是否黑名单 0-外呼全部用户 1-不呼叫服务免打扰用户 2-不呼叫营销免打扰用户 3-不呼叫所有免打扰用户（服务+营销）
//        JSONObject IVRCALLTASK_REQ = new JSONObject();
//        IVRCALLTASK_REQ.put("systemID", systemID);
//        IVRCALLTASK_REQ.put("uuid", uuid);
//        IVRCALLTASK_REQ.put("calledNo", calledNo);
//        IVRCALLTASK_REQ.put("explicitNo", explicitNo);
//        IVRCALLTASK_REQ.put("msg1", msg1);
//        IVRCALLTASK_REQ.put("whetherFrequency", whetherFrequency);
//        IVRCALLTASK_REQ.put("whetherBlack", whetherBlack);
//        IVRCALLTASK_REQ.put("messageContent", msg1);
//
//        JSONObject UNI_BSS_BODY = new JSONObject();
//        UNI_BSS_BODY.put("IVRCALLTASK_REQ", IVRCALLTASK_REQ);
//
//        JSONObject MEDIA_INFO = new JSONObject();
//        MEDIA_INFO.put("MEDIA_INFO", "");
//        JSONObject obj = new JSONObject();
//        obj.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
//        obj.put("UNI_BSS_BODY", UNI_BSS_BODY);
//        obj.put("UNI_BSS_ATTACHED", MEDIA_INFO);
//        System.out.println("入参：" + obj.toJSONString());
//        try {
//            String post = HttpClientUtil.post(url, obj);
//            System.out.println("出参：" + post);
//        } catch (HttpClientException e) {
//            throw new RuntimeException(e);
//        }
//    }
}





