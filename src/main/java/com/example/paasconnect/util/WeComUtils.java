package com.example.paasconnect.util;

import com.alibaba.fastjson.JSONObject;
import com.example.paasconnect.exception.HttpClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Random;

/**
 * @Description: 企微短消息发送
 * @param: null
 * @return:
 * @Author: guojian
 * @Date 2025/6/30 16:56
 */
public class WeComUtils {
    private final static Logger log = LoggerFactory.getLogger(WeComUtils.class);
    public static String generateRandomNumber(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int digit = random.nextInt(10); // 生成0到9之间的随机数字
            sb.append(digit);
        }

        return sb.toString();
    }

   public static boolean sendWechar(String mobile, String content) {
        //生产入口
        String url = "http://*************:8000/api/chinaUnicom/bj11/weCom/send/v1";
        String key = "2xp78W2A3w69hxsan905tfOnnD0i0AOO";
        int id = 75;
        String sender = "xxzx";
        String app_id = "VEwVtF37AY";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss SSS");
        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyyMMddhhmmssSSS");
        long l = System.currentTimeMillis();
        String TIMESTAMP = dateFormat.format(l);
        String TRANS_ID = dateFormat1.format(l) + generateRandomNumber(6);
        String value = "APP_ID" + app_id + "TIMESTAMP" + TIMESTAMP + "TRANS_ID" + TRANS_ID + key;
        System.out.println("加密前：" + value);
        String token = MD5Encryption.encrypt(value);
        JSONObject UNI_BSS_HEAD = new JSONObject();
        UNI_BSS_HEAD.put("APP_ID", app_id);
        UNI_BSS_HEAD.put("TIMESTAMP", TIMESTAMP);
        UNI_BSS_HEAD.put("TRANS_ID", TRANS_ID);
        UNI_BSS_HEAD.put("TOKEN", token);
        //body 拼接
        String systemID = "YWJKPT";
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("YYYYMMDDhhmmss");
        System.out.println(dateFormat2.format(l));
        String uuid = dateFormat2.format(l) + systemID + generateRandomNumber(4);
        System.out.println(uuid);

        JSONObject SEND_REQ = new JSONObject();
        SEND_REQ.put("sender", sender);
        SEND_REQ.put("templateId", id);
        SEND_REQ.put("recver", mobile);
        SEND_REQ.put("recverType", "1");
        SEND_REQ.put("title", "");
        SEND_REQ.put("text", content);
        SEND_REQ.put("desc", "");
        SEND_REQ.put("image", "");
        SEND_REQ.put("url", "");
        SEND_REQ.put("remark1", "");
        SEND_REQ.put("remark2", "");
        SEND_REQ.put("remark3", "");
        SEND_REQ.put("remark4", "");
        SEND_REQ.put("remark5", "");
        SEND_REQ.put("remark6", "");
        SEND_REQ.put("remark7", "");
        SEND_REQ.put("remark8", "");
        SEND_REQ.put("remark9", "");
        SEND_REQ.put("remark10", "");

        JSONObject UNI_BSS_BODY = new JSONObject();
        UNI_BSS_BODY.put("SEND_REQ", SEND_REQ);

        JSONObject MEDIA_INFO = new JSONObject();
        MEDIA_INFO.put("MEDIA_INFO", "");
        JSONObject obj = new JSONObject();
        obj.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
        obj.put("UNI_BSS_BODY", UNI_BSS_BODY);
        obj.put("UNI_BSS_ATTACHED", MEDIA_INFO);
        log.info("入参：" + obj.toJSONString());
        try {
            String response = HttpClientUtil.post(url, obj);
            log.info("企微发送响应：" + response);
            
            // 判断发送是否成功
            boolean success = isWeComSendSuccess(response);
            if (success) {
                log.info("企微消息发送成功");
            } else {
                log.error("企微消息发送失败：" + response);
            }
            return success;
            
        } catch (HttpClientException e) {
            log.error("企微消息发送异常", e);
            return false;
        }
   }

    public static boolean isWeComSendSuccess(String response) {
        try {
            JSONObject json = JSONObject.parseObject(response);
            
            // 检查头部响应码
            String respCode = json.getJSONObject("UNI_BSS_HEAD").getString("RESP_CODE");
            if (!"00000".equals(respCode)) {
                return false;
            }
            
            // 检查业务响应码
            JSONObject sendRsp = json.getJSONObject("UNI_BSS_BODY").getJSONObject("SEND_RSP");
            String retCode = sendRsp.getString("retCode");
            Boolean success = sendRsp.getBoolean("success");
            
            if (!"000000".equals(retCode) || !Boolean.TRUE.equals(success)) {
                return false;
            }
            
            // 检查微信接口响应
            JSONObject data = sendRsp.getJSONObject("data");
            Integer errcode = data.getInteger("errcode");
            Boolean wxSuccess = data.getBoolean("success");
            
            return errcode != null && errcode == 0 && Boolean.TRUE.equals(wxSuccess);
            
        } catch (Exception e) {
            log.error("解析企业微信响应失败", e);
            return false;
        }
    }
    // public static void main(String[] args) {
    //     System.out.println(WeComUtils.sendWechar("15701574012", "ceshi o1"));
    // }

//     public static void main(String[] args) {
//         //生产入口
//        String url = "http://*************:8000/api/chinaUnicom/bj11/weCom/send/v1";
//        String key = "2xp78W2A3w69hxsan905tfOnnD0i0AOO";
//        int id = 75;
//        String sender = "xxzx";
// //        生产环境：id：75 sender：xxzx

// //        测试入口
// //        String url = "http://10.124.150.230:8000/api/chinaUnicom/bj11/weCom/send/v1";
//         // String key = "AAo3A7JtOmgE7rj4nWJ4zM2jBXFfN8Sm"; //测试
// //        int id = 59;
// //        String sender = "xxzx";

//         //授权码 用的是贾梦园的账号q
//         String app_id = "VEwVtF37AY";
//         SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss SSS");
//         SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyyMMddhhmmssSSS");
//         long l = System.currentTimeMillis();
//         String TIMESTAMP = dateFormat.format(l);
//         String TRANS_ID = dateFormat1.format(l) + generateRandomNumber(6);
//         String value = "APP_ID" + app_id + "TIMESTAMP" + TIMESTAMP + "TRANS_ID" + TRANS_ID + key;
//         System.out.println("加密前：" + value);
//         String token = MD5Encryption.encrypt(value);
//         JSONObject UNI_BSS_HEAD = new JSONObject();
//         UNI_BSS_HEAD.put("APP_ID", app_id);
//         UNI_BSS_HEAD.put("TIMESTAMP", TIMESTAMP);
//         UNI_BSS_HEAD.put("TRANS_ID", TRANS_ID);
//         UNI_BSS_HEAD.put("TOKEN", token);
//         //body 拼接
//         String systemID = "YWJKPT";
//         SimpleDateFormat dateFormat2 = new SimpleDateFormat("YYYYMMDDhhmmss");
//         System.out.println(dateFormat2.format(l));
//         String uuid = dateFormat2.format(l) + systemID + generateRandomNumber(4);
//         System.out.println(uuid);

//         JSONObject SEND_REQ = new JSONObject();
//         SEND_REQ.put("sender", sender);
//         SEND_REQ.put("templateId", id);
//         SEND_REQ.put("recver", "15701574012|13261690105");
//         SEND_REQ.put("recverType", "1");
//         SEND_REQ.put("title", "");
//         SEND_REQ.put("text", "业务监控能开实时接口测试");
//         SEND_REQ.put("desc", "");
//         SEND_REQ.put("image", "");
//         SEND_REQ.put("url", "");
//         SEND_REQ.put("remark1", "");
//         SEND_REQ.put("remark2", "");
//         SEND_REQ.put("remark3", "");
//         SEND_REQ.put("remark4", "");
//         SEND_REQ.put("remark5", "");
//         SEND_REQ.put("remark6", "");
//         SEND_REQ.put("remark7", "");
//         SEND_REQ.put("remark8", "");
//         SEND_REQ.put("remark9", "");
//         SEND_REQ.put("remark10", "");

//         JSONObject UNI_BSS_BODY = new JSONObject();
//         UNI_BSS_BODY.put("SEND_REQ", SEND_REQ);

//         JSONObject MEDIA_INFO = new JSONObject();
//         MEDIA_INFO.put("MEDIA_INFO", "");
//         JSONObject obj = new JSONObject();
//         obj.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
//         obj.put("UNI_BSS_BODY", UNI_BSS_BODY);
//         obj.put("UNI_BSS_ATTACHED", MEDIA_INFO);
//         System.out.println("入参：" + obj.toJSONString());
//         try {
//             String post = HttpClientUtil.post(url, obj);
//             System.out.println("出参：" + post);
//         } catch (HttpClientException e) {
//             throw new RuntimeException(e);
//         }
//     }
}





