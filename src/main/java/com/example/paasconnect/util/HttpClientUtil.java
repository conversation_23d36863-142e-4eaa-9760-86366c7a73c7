package com.example.paasconnect.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.example.paasconnect.exception.HttpClientException;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URI;
import java.net.URL;

/**
 * 
 * <p>
 * Title: HttpClientUtil
 * </p>
 * <p>
 * Description: HttpClient工具类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2013
 * </p>
 * <p>
 * Company: SI-TECH
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @createtime 2013-6-16 上午10:17:57
 * 
 */
public class HttpClientUtil {
	private static final Log LOG = LogFactory.getLog(HttpClientUtil.class);
	private static final String CODE_ENCODING = "UTF-8";

	/**
	 * 
	 * @Title: post
	 * @Description: Http-Post方式提交数据 用于新建操作
	 * @param
	 * @return String
	 * @throws
	 * <AUTHOR>
	 * @version 1.0
	 * @throws HttpClientException
	 * @createtime 2013-6-16 上午10:18:10
	 */
	public static <T> String post(String url, T clazzt) throws HttpClientException {
		HttpParams httpParams = new BasicHttpParams();
		httpParams.setParameter("charset", CODE_ENCODING);
		HttpClient client = new DefaultHttpClient(httpParams);
		//请求超时
		client.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 300000);
		//读取超时
		client.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, 300000);

		HttpPost post = new HttpPost(url);
		String response = "";
		try {
			post.addHeader("Content-Type", "application/json;charset=" + CODE_ENCODING);
			post.setHeader("Accept", "application/json");
			post.setHeader("Content-Type", "application/json;charset=" + CODE_ENCODING);
			// 转换对象To Json字符串
			String clazzToJson = JSON.toJSONString(clazzt);
			// 转码,防止中文乱码
			System.out.println("Post " + url + " Data " + clazzToJson);
			LOG.info("Post " + url + " Data " + clazzToJson);
			StringEntity s = new StringEntity(clazzToJson, CODE_ENCODING);
			post.setEntity(s);
			HttpResponse res = client.execute(post);
			int statusCode = res.getStatusLine().getStatusCode();
			System.out.println("Post " + url + " Data " + clazzToJson + " ResultCode : " + statusCode);
			LOG.info("Post " + url + " Data " + clazzToJson + " ResultCode : " + statusCode);
			// 判断返回编码是否为200
			if (statusCode != 200 && statusCode != 201) {
				throw new HttpClientException("Post " + url + "Error!Response Code " + statusCode);
			}
			HttpEntity entity = res.getEntity();
			response = EntityUtils.toString(entity, CODE_ENCODING);
		} catch (Exception e) {
			LOG.error("Post " + url + "Error!" + e.getMessage(), e);
			throw new HttpClientException("Post " + url + "Error!" + e.getMessage(), e);
		}
		return response;
	}

	/**
	 * 
	 * @Title: delete
	 * @Description: Http Delete请求 用于删除操作
	 * @param
	 * @return String
	 * @throws
	 * <AUTHOR>
	 * @version 1.0
	 * @throws HttpClientException
	 * @createtime 2013-7-24 上午9:14:18
	 */
	public static <T> String delete(String url) throws HttpClientException {
		HttpParams httpParams = new BasicHttpParams();
		httpParams.setParameter("charset", CODE_ENCODING);
		HttpClient client = new DefaultHttpClient(httpParams);
		HttpDelete delete = new HttpDelete(url);
		String response = "";
		try {
			delete.addHeader("Content-Type", "application/json;charset=" + CODE_ENCODING);
			delete.setHeader("Accept", "application/json");
			delete.setHeader("Content-Type", "application/json;charset=" + CODE_ENCODING);
			// 转码,防止中文乱码
			System.out.println("Delete " + url);
			HttpResponse res = client.execute(delete);
			int statusCode = res.getStatusLine().getStatusCode();
			System.out.println("Delete " + url + " ResultCode : " + statusCode);
			LOG.info("Delete " + url + " ResultCode : " + statusCode);
			// 判断返回编码是否为200
			if (statusCode != 200 && statusCode != 201) {
				throw new HttpClientException("Delete " + url + "Error!Response Code " + statusCode);
			}
			HttpEntity entity = res.getEntity();
			response = EntityUtils.toString(entity, CODE_ENCODING);
		} catch (Exception e) {
			LOG.error("Delete " + url + "Error!" + e.getMessage(), e);
			throw new HttpClientException("Delete " + url + "Error!" + e.getMessage(), e);
		}
		return response;
	}

	/**
	 * 
	 * @Title: get
	 * @Description: Http get方式获取数据 用于查询
	 * @param
	 * @return String
	 * @throws
	 * <AUTHOR>
	 * @version 1.0
	 * @throws HttpClientException
	 * @createtime 2013-6-16 上午10:26:20
	 */
	public static String get(String url) throws HttpClientException {
		HttpParams httpParams = new BasicHttpParams();
		httpParams.setParameter("charset", CODE_ENCODING);
		HttpClient client = new DefaultHttpClient(httpParams);
		HttpGet get = new HttpGet(url);
		String response = "";
		try {
			get.addHeader("Content-Type", "application/json;charset=" + CODE_ENCODING);
			get.setHeader("Accept", "application/json");
			get.setHeader("Content-Type", "application/json;charset=" + CODE_ENCODING);
			HttpResponse res = client.execute(get);
			int statusCode = res.getStatusLine().getStatusCode();
			System.out.println("Get " + url + " ResultCode : " + statusCode);
			LOG.info("Get " + url + " ResultCode : " + statusCode);
			// 判断返回编码是否为200
			if (statusCode != 200 && statusCode != 201) {
				throw new HttpClientException("Get " + url + "Error!Response Code " + statusCode);
			}
			HttpEntity entity = res.getEntity();
			response = EntityUtils.toString(entity, CODE_ENCODING);
		} catch (Exception e) {
			LOG.error("Get " + url + "异常！" + e.getMessage(), e);
			throw new HttpClientException("Get " + url + "异常！" + e.getMessage(), e);
		}
		return response;
	}

	/**
	 * 通过url来获取
	 * 
	 * @param url
	 * @return
	 */
	public static String getByUrl(String url) {
		HttpParams httpParams = new BasicHttpParams();
		httpParams.setParameter("Accept", "application/json, text/plain, */*");
		httpParams
				.setParameter(
						"Cookie",
						"LastAccount=liubeibei%40bj.cmcc; BIGipServer~VLB_4624747767~f5-479eeeb8-87c3-4b3b-9c7e-814cf0b8dd60=rd4o00000000000000000000ffff0afcc92bo80; JLWZAUTH=AB58792A0EE8BE2A02630073003100300030000000A00266FF096BD60100A03AB324236BD6014100630063006F0075006E0074005200650061006C003D006C00690075006200650069006200650069003B004D00610069006E00490064003D006C0069007500620065006900620065006900400062006A002E0063006D006300630000002F000000; jlwznguid=dAeQDMpD8unB4HqBgEqDG4R0phDp7B9%2FJuU4Xbj8b6%2B3YXYQlkcLJwacERQ4AK%2Beevq9O4kybrsqsuVEpoM%2FzcJeA%2B4JvCxzRV%2FyafFCukCkxwaC6Is3D%2BqD0lIMTz7l; ASPSESSIONIDASSTTSQR=GPEHKMPAANDLDPLBFLAMFAKD");
		HttpClient client = new DefaultHttpClient(httpParams);
		String response = "";
		try {
			URL u = new URL(url);
			URI uri = new URI(u.getProtocol(), null, u.getHost(), u.getPort(), u.getPath(), u.getQuery(), null);
			HttpGet get = new HttpGet(uri);
			HttpResponse res = client.execute(get);
			System.out.println(url);
			HttpEntity entity = res.getEntity();
			response = EntityUtils.toString(entity, CODE_ENCODING);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return response;
	}

		public static String sendPostRequest(String url, String password) {
			HttpClient client = new DefaultHttpClient();
			HttpPost request = new HttpPost(url);
			request.addHeader("Content-Type", "application/json");

			try {
				HttpEntity entity = new StringEntity("{\"password\": \"" + password + "\"}", ContentType.APPLICATION_JSON);
				request.setEntity(entity);

				HttpResponse response = client.execute(request);
				if (response.getEntity()!= null) {
					return EntityUtils.toString(response.getEntity());
				}
			} catch (IOException e) {
				e.printStackTrace();
			}

			return null;
		}

//		public static void main(String[] args) {
//			String url = "http://132.91.202.206:9000/ws/devicesList";
//			String password = "zlGW6OfKLXeaNuYHt/0i4fU6mnSwSw6Li7gxrglm1eY=";
//			String response = sendPostRequest(url, password);
//			System.out.println(response);
//		}
}
