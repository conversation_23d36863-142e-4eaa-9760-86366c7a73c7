package com.example.paasconnect.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 日志工具类
 * 提供日志相关的工具方法
 */
public class LogUtil {
    private static final Logger logger = LoggerFactory.getLogger(LogUtil.class);
    // 根据环境自动选择日志目录
    private static final String LOG_HOME = getLogHomeDir();
    private static final String LOG_FILE_PREFIX = "alarm";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    /**
     * 获取适合当前环境的日志目录
     * 生产环境使用 /app/alarm_rule_8081/logs
     * 开发环境使用用户目录下的 logs/alarm_rule
     */
    private static String getLogHomeDir() {
        String configuredPath = "/app/alarm_rule_8081/logs";
        // 尝试创建目录
        File configuredDir = new File(configuredPath);
        if (configuredDir.exists() || configuredDir.mkdirs()) {
            return configuredPath;
        }
        
        // 如果无法创建配置的目录，则使用用户目录
        String userHome = System.getProperty("user.home");
        String devPath = userHome + "/logs/alarm_rule";
        File devDir = new File(devPath);
        if (!devDir.exists()) {
            devDir.mkdirs();
        }
        
        logger.warn("无法使用配置的日志目录 {}，将使用开发环境日志目录: {}", configuredPath, devPath);
        return devPath;
    }
    
    /**
     * 获取日志目录路径
     * @return 日志目录路径
     */
    public static String getLogHome() {
        return LOG_HOME;
    }
    
    /**
     * 获取指定日期的日志文件路径
     * @param date 日期
     * @return 日志文件路径
     */
    public static String getLogFileByDate(LocalDate date) {
        String dateStr = date.format(DATE_FORMATTER);
        return String.format("%s/%s-%s.log", LOG_HOME, LOG_FILE_PREFIX, dateStr);
    }
    
    /**
     * 获取今天的日志文件路径
     * @return 今天的日志文件路径
     */
    public static String getTodayLogFile() {
        return getLogFileByDate(LocalDate.now());
    }
    
    /**
     * 获取当前的主日志文件路径
     * @return 主日志文件路径
     */
    public static String getCurrentLogFile() {
        return String.format("%s/%s.log", LOG_HOME, LOG_FILE_PREFIX);
    }
    
    /**
     * 获取错误日志文件路径
     * @return 错误日志文件路径
     */
    public static String getErrorLogFile() {
        return String.format("%s/error.log", LOG_HOME);
    }
    
    /**
     * 获取指定日期的错误日志文件路径
     * @param date 日期
     * @return 错误日志文件路径
     */
    public static String getErrorLogFileByDate(LocalDate date) {
        String dateStr = date.format(DATE_FORMATTER);
        return String.format("%s/error-%s.log", LOG_HOME, dateStr);
    }
    
    /**
     * 创建日志目录（如果不存在）
     */
    public static void createLogDirectoryIfNotExists() {
        File logDir = new File(LOG_HOME);
        if (!logDir.exists()) {
            boolean created = logDir.mkdirs();
            if (created) {
                logger.info("成功创建日志目录: {}", LOG_HOME);
            } else {
                logger.error("无法创建日志目录: {}", LOG_HOME);
            }
        }
    }
    
    /**
     * 清理超过指定天数的日志文件
     * @param days 保留天数
     */
    public static void cleanupOldLogFiles(int days) {
        try {
            File logDir = new File(LOG_HOME);
            if (!logDir.exists() || !logDir.isDirectory()) {
                logger.warn("日志目录不存在: {}", LOG_HOME);
                return;
            }
            
            LocalDate cutoffDate = LocalDate.now().minusDays(days);
            
            File[] logFiles = logDir.listFiles((dir, name) -> 
                    (name.startsWith(LOG_FILE_PREFIX + "-") || name.startsWith("error-")) && name.endsWith(".log"));
            
            if (logFiles != null) {
                for (File file : logFiles) {
                    String fileName = file.getName();
                    // 提取日期部分
                    String dateStr = null;
                    if (fileName.startsWith(LOG_FILE_PREFIX + "-")) {
                        dateStr = fileName.substring((LOG_FILE_PREFIX + "-").length(), fileName.length() - 4);
                    } else if (fileName.startsWith("error-")) {
                        dateStr = fileName.substring("error-".length(), fileName.length() - 4);
                    }
                    
                    if (dateStr != null) {
                        try {
                            LocalDate fileDate = LocalDate.parse(dateStr, DATE_FORMATTER);
                            if (fileDate.isBefore(cutoffDate)) {
                                boolean deleted = file.delete();
                                if (deleted) {
                                    logger.info("已删除过期日志文件: {}", fileName);
                                } else {
                                    logger.warn("无法删除过期日志文件: {}", fileName);
                                }
                            }
                        } catch (Exception e) {
                            logger.warn("无法解析日志文件日期: {}", fileName, e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("清理日志文件时发生错误", e);
        }
    }
    
    /**
     * 获取所有可用的日志文件列表
     * @return 日志文件列表
     */
    public static List<String> getAvailableLogFiles() {
        try {
            File logDir = new File(LOG_HOME);
            if (!logDir.exists() || !logDir.isDirectory()) {
                logger.warn("日志目录不存在: {}", LOG_HOME);
                return Collections.emptyList();
            }
            
            File[] logFiles = logDir.listFiles((dir, name) -> 
                    (name.startsWith(LOG_FILE_PREFIX) || name.startsWith("error")) && name.endsWith(".log"));
            
            if (logFiles != null) {
                return Arrays.stream(logFiles)
                        .sorted(Comparator.comparing(File::lastModified).reversed())
                        .map(File::getName)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error("获取日志文件列表时发生错误", e);
        }
        return Collections.emptyList();
    }
    
    /**
     * 读取日志文件内容
     * @param logFile 日志文件路径
     * @param maxLines 最大行数
     * @return 日志内容
     */
    public static List<String> readLogFile(String logFile, int maxLines) {
        try {
            Path path = Paths.get(logFile);
            if (!Files.exists(path)) {
                logger.warn("日志文件不存在: {}", logFile);
                return Collections.emptyList();
            }
            
            return Files.lines(path)
                    .limit(maxLines)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("读取日志文件时发生错误: {}", logFile, e);
            return Collections.emptyList();
        }
    }
} 