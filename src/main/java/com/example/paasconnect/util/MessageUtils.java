package com.example.paasconnect.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.example.paasconnect.exception.HttpClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Random;

/**
 * @Description: 短信发送工具类
 * @param: null
 * @return:
 * @Author: guojian
 * @Date 2023/9/22 14:15
 */
public class MessageUtils {

    private final static Logger log = LoggerFactory.getLogger(MessageUtils.class);
    public static String generateRandomNumber(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int digit = random.nextInt(10); // 生成0到9之间的随机数字
            sb.append(digit);
        }

        return sb.toString();
    }

    /**
     * @Description: 对接集团短信能开，进行短信发送
     * @param: phone
     * @param: content
     * @return: java.lang.String
     * @Author: guojian
     * @Date 2024/8/26 16:29
     */
    public static String sendMessages(String phone, String content){
        String result = "";
        //生产入口
        String url = "http://*************:8000/api/chinaUnicom/bj11/messageManagement/sm/submit/v1";
        String key = "2xp78W2A3w69hxsan905tfOnnD0i0AOO"; //生产
        String app_id = "VEwVtF37AY";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS");
        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyyMMddHHmmss");
        long l = System.currentTimeMillis();
        String TIMESTAMP = dateFormat.format(l);
        String TRANS_ID = dateFormat1.format(l)+generateRandomNumber(6);
        String value = "APP_ID"+app_id+"TIMESTAMP"+TIMESTAMP+"TRANS_ID"+TRANS_ID+key;
//        System.out.println("加密前："+value);
        String token = MD5Encryption.encrypt(value);
        JSONObject UNI_BSS_HEAD = new JSONObject();
        UNI_BSS_HEAD.put("APP_ID",app_id);
        UNI_BSS_HEAD.put("TIMESTAMP",TIMESTAMP);
        UNI_BSS_HEAD.put("TRANS_ID",TRANS_ID);
        UNI_BSS_HEAD.put("TOKEN",token);
        //body 拼接
        JSONObject MESSAGE_INFO = new JSONObject();
        MESSAGE_INFO.put("ORIGIN_DOMAIN","80");//短信编码
        MESSAGE_INFO.put("SEQ_ID",dateFormat1.format(l));//流水号
        MESSAGE_INFO.put("RECV_OBJECT",phone);//电话号码
//        log.info("手机号："+ phone);
        MESSAGE_INFO.put("MESSAGE_CONTENT",content);//消息内容
        MESSAGE_INFO.put("SEND_TIME",dateFormat2.format(l));//短信发送时间
        MESSAGE_INFO.put("MSG_FLAG","0");//是否10010发送（1:是0否）
        MESSAGE_INFO.put("MSG_PRIORITY","5");//短信发送优先级（默认:5）

        JSONObject SUBMIT_MESSAGE_REQ = new JSONObject();
        SUBMIT_MESSAGE_REQ.put("MESSAGE_INFO",MESSAGE_INFO);

        JSONObject SM_SUBMIT_REQ = new JSONObject();
        SM_SUBMIT_REQ.put("SUBMIT_MESSAGE_REQ",SUBMIT_MESSAGE_REQ);

        JSONObject UNI_BSS_BODY = new JSONObject();
        UNI_BSS_BODY.put("SM_SUBMIT_REQ",SM_SUBMIT_REQ);

        JSONObject MEDIA_INFO = new JSONObject();
        MEDIA_INFO.put("MEDIA_INFO","");
        JSONObject obj = new JSONObject();
        obj.put("UNI_BSS_HEAD",UNI_BSS_HEAD);
        obj.put("UNI_BSS_BODY",UNI_BSS_BODY);
        obj.put("UNI_BSS_ATTACHED",MEDIA_INFO);
//        System.out.println("入参："+obj.toJSONString());
        log.info("入参："+obj.toJSONString());
        try {
            result = HttpClientUtil.post(url, obj);
//            System.out.println("出参："+result);
            log.info("出参："+result);
        } catch (HttpClientException e) {
            throw new RuntimeException(e);
        }
        return result;
    }
//    public static void main(String[] args) {
//        //测试入口
////        String url = "http://10.124.150.230:8000/api/chinaUnicom/bj11/messageManagement/sm/submit/v1";
////        String key = "AAo3A7JtOmgE7rj4nWJ4zM2jBXFfN8Sm"; //测试
//        //生产入口
//        String url = "http://*************:8000/api/chinaUnicom/bj11/messageManagement/sm/submit/v1";
//        String key = "2xp78W2A3w69hxsan905tfOnnD0i0AOO"; //生产
//        //授权码 用的是贾梦园的账号
////        String app_id = "VEwVtF37AY";
//        String app_id = "VEwVtF37AY";
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss SSS");
//        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyyMMddhhmmssSSS");
//        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyyMMddhhmmss");
//        long l = System.currentTimeMillis();
//        String TIMESTAMP = dateFormat.format(l);
//        String TRANS_ID = dateFormat1.format(l)+generateRandomNumber(6);
//        String value = "APP_ID"+app_id+"TIMESTAMP"+TIMESTAMP+"TRANS_ID"+TRANS_ID+key;
//        System.out.println("加密前："+value);
//        String token = MD5Encryption.encrypt(value);
//        JSONObject UNI_BSS_HEAD = new JSONObject();
//        UNI_BSS_HEAD.put("APP_ID",app_id);
//        UNI_BSS_HEAD.put("TIMESTAMP",TIMESTAMP);
//        UNI_BSS_HEAD.put("TRANS_ID",TRANS_ID);
//        UNI_BSS_HEAD.put("TOKEN",token);
//        //body 拼接
//        JSONObject MESSAGE_INFO = new JSONObject();
//        MESSAGE_INFO.put("ORIGIN_DOMAIN","80");//短信编码
//        MESSAGE_INFO.put("SEQ_ID",dateFormat1.format(l));//流水号
//        MESSAGE_INFO.put("RECV_OBJECT","15701574012");//电话号码、
//        MESSAGE_INFO.put("MESSAGE_CONTENT","测试01");//消息内容
//        MESSAGE_INFO.put("SEND_TIME",dateFormat2.format(l));//短信发送时间
//        MESSAGE_INFO.put("MSG_FLAG","0");//是否10010发送（1:是0否）
//        MESSAGE_INFO.put("MSG_PRIORITY","5");//短信发送优先级（默认:5）
//
//        JSONObject SUBMIT_MESSAGE_REQ = new JSONObject();
//        SUBMIT_MESSAGE_REQ.put("MESSAGE_INFO",MESSAGE_INFO);
//
//        JSONObject SM_SUBMIT_REQ = new JSONObject();
//        SM_SUBMIT_REQ.put("SUBMIT_MESSAGE_REQ",SUBMIT_MESSAGE_REQ);
//
//        JSONObject UNI_BSS_BODY = new JSONObject();
//        UNI_BSS_BODY.put("SM_SUBMIT_REQ",SM_SUBMIT_REQ);
//
//        JSONObject MEDIA_INFO = new JSONObject();
//        MEDIA_INFO.put("MEDIA_INFO","");
//        JSONObject obj = new JSONObject();
//        obj.put("UNI_BSS_HEAD",UNI_BSS_HEAD);
//        obj.put("UNI_BSS_BODY",UNI_BSS_BODY);
//        obj.put("UNI_BSS_ATTACHED",MEDIA_INFO);
//        System.out.println("入参："+obj.toJSONString());
//        try {
//            String post = HttpClientUtil.post(url, obj);
//            System.out.println("出参："+post);
//            JSONObject jsonObject = JSONObject.parseObject(post);
//            JSONObject uniOspBody = jsonObject.getJSONObject("UNI_BSS_BODY");
//            JSONObject SM_SUBMIT_RSP = uniOspBody.getJSONObject("SM_SUBMIT_RSP");
//            JSONArray submitMessageRsp = SM_SUBMIT_RSP.getJSONArray("SUBMIT_MESSAGE_RSP");
//            String rspCode = submitMessageRsp.getJSONObject(0).getString("RSP_CODE");
//            String result1 = submitMessageRsp.getJSONObject(0).getString("RSP_DESC");
//            String string = jsonObject.getJSONObject("UNI_BSS_HEAD").getString("TIMESTAMP");
//            System.out.println("出参："+rspCode);
//            System.out.println("出参："+result1);
//            System.out.println("出参："+string);
//
//        } catch (HttpClientException e) {
//            throw new RuntimeException(e);
//        }
//    }
public static void main(String[] args) {
        //测试入口
//        String url = "http://10.124.150.230:8000/api/chinaUnicom/bj11/messageManagement/sm/submit/v1";
//        String key = "AAo3A7JtOmgE7rj4nWJ4zM2jBXFfN8Sm"; //测试
        //生产入口
        String url = "http://*************:8000/api/chinaUnicom/bj11/messageManagement/sm/submit/v1";
        String key = "2xp78W2A3w69hxsan905tfOnnD0i0AOO"; //生产
        //授权码 用的是贾梦园的账号
//        String app_id = "VEwVtF37AY";
        String app_id = "VEwVtF37AY";
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss SSS");
        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyyMMddhhmmssSSS");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyyMMddhhmmss");
        long l = System.currentTimeMillis();
        String TIMESTAMP = dateFormat.format(l);
        String TRANS_ID = dateFormat1.format(l)+generateRandomNumber(6);
        String value = "APP_ID"+app_id+"TIMESTAMP"+TIMESTAMP+"TRANS_ID"+TRANS_ID+key;
        System.out.println("加密前："+value);
        String token = MD5Encryption.encrypt(value);
        JSONObject UNI_BSS_HEAD = new JSONObject();
        UNI_BSS_HEAD.put("APP_ID",app_id);
        UNI_BSS_HEAD.put("TIMESTAMP",TIMESTAMP);
        UNI_BSS_HEAD.put("TRANS_ID",TRANS_ID);
        UNI_BSS_HEAD.put("TOKEN",token);
        //body 拼接
        JSONObject MESSAGE_INFO = new JSONObject();
        MESSAGE_INFO.put("ORIGIN_DOMAIN","80");//短信编码
        MESSAGE_INFO.put("SEQ_ID",dateFormat1.format(l));//流水号
        MESSAGE_INFO.put("RECV_OBJECT","");//电话号码、
        MESSAGE_INFO.put("MESSAGE_CONTENT","");//消息内容
        MESSAGE_INFO.put("SEND_TIME",dateFormat2.format(l));//短信发送时间
        MESSAGE_INFO.put("MSG_FLAG","0");//是否10010发送（1:是0否）
        MESSAGE_INFO.put("MSG_PRIORITY","5");//短信发送优先级（默认:5）

        JSONObject SUBMIT_MESSAGE_REQ = new JSONObject();
        SUBMIT_MESSAGE_REQ.put("MESSAGE_INFO",MESSAGE_INFO);

        JSONObject SM_SUBMIT_REQ = new JSONObject();
        SM_SUBMIT_REQ.put("SUBMIT_MESSAGE_REQ",SUBMIT_MESSAGE_REQ);

        JSONObject UNI_BSS_BODY = new JSONObject();
        UNI_BSS_BODY.put("SM_SUBMIT_REQ",SM_SUBMIT_REQ);

        JSONObject MEDIA_INFO = new JSONObject();
        MEDIA_INFO.put("MEDIA_INFO","");
        JSONObject obj = new JSONObject();
        obj.put("UNI_BSS_HEAD",UNI_BSS_HEAD);
        obj.put("UNI_BSS_BODY",UNI_BSS_BODY);
        obj.put("UNI_BSS_ATTACHED",MEDIA_INFO);
        System.out.println("入参："+obj.toJSONString());
        try {
            String post = HttpClientUtil.post(url, obj);
            System.out.println("出参："+post);
            JSONObject jsonObject = JSONObject.parseObject(post);
            JSONObject uniOspBody = jsonObject.getJSONObject("UNI_BSS_BODY");
            JSONObject SM_SUBMIT_RSP = uniOspBody.getJSONObject("SM_SUBMIT_RSP");
            JSONArray submitMessageRsp = SM_SUBMIT_RSP.getJSONArray("SUBMIT_MESSAGE_RSP");
            String rspCode = submitMessageRsp.getJSONObject(0).getString("RSP_CODE");
            String result1 = submitMessageRsp.getJSONObject(0).getString("RSP_DESC");
            String string = jsonObject.getJSONObject("UNI_BSS_HEAD").getString("TIMESTAMP");
            System.out.println("出参："+rspCode);
            System.out.println("出参："+result1);
            System.out.println("出参："+string);

        } catch (HttpClientException e) {
            throw new RuntimeException(e);
        }
    }
}





