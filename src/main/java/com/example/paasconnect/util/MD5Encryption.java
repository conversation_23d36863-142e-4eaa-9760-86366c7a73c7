package com.example.paasconnect.util;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5Encryption {

    /**
     * @Description: 进行md5(32位小写)加密
     * @param: input
     * @return: java.lang.String
     * @Author: g<PERSON><PERSON><PERSON>
     * @Date 2023/8/24 13:44
     */
    public static String encrypt(String input) {
        try {
            // 创建 MessageDigest 实例并指定算法为 MD5
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 将输入字符串转换为字节数组
            byte[] inputBytes = input.getBytes();

            // 计算 MD5 哈希值
            byte[] hashBytes = md.digest(inputBytes);

            // 将字节数组转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            // 返回加密后的结果（32位小写）
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        return null;
    }

//    public static void main(String[] args) {
//        String input = "APP_IDVEwVtF37AYTIMESTAMP2023-08-24 09:30:05 100TRANS_ID20230824093005100335423AAo3A7JtOmgE7rj4nWJ4zM2jBXFfN8Sm";
//        String encrypted = encrypt(input);
//        System.out.println("Encrypted: " + encrypted);
//    }
}