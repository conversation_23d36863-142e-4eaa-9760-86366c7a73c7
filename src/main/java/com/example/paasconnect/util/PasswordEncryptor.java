package com.example.paasconnect.util;

import org.jasypt.util.text.BasicTextEncryptor;

public class PasswordEncryptor {
    private static String encrypt(String salt,String encryptStr){
        BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
        //加密所需的salt
        textEncryptor.setPassword(salt);
        //要加密的数据
        return textEncryptor.encrypt(encryptStr);
    }
    private static String decrypt(String salt,String encryptStr){
        BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
        //加密所需的salt
        textEncryptor.setPassword(salt);
        //要加密的数据
        return textEncryptor.decrypt(encryptStr);
    }
}