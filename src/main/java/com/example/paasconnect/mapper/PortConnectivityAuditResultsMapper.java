package com.example.paasconnect.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.paasconnect.entity.PortConnectivityAuditResults;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface PortConnectivityAuditResultsMapper extends BaseMapper<PortConnectivityAuditResults> {

    List<PortConnectivityAuditResults> seleteByTime(String createTime);

    List<PortConnectivityAuditResults> gettbLogPnmsPerformanceByID(String name);
}
