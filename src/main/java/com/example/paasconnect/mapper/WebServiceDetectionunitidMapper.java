package com.example.paasconnect.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.paasconnect.entity.WebServiceDetectionunitid;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import com.example.paasconnect.entity.WebServiceTaskresult;

@Mapper
public interface WebServiceDetectionunitidMapper extends BaseMapper<WebServiceDetectionunitid> {
    public WebServiceDetectionunitid getUnitId(String detectionUnitId);
    public List<WebServiceDetectionunitid>  getPcUnitId();
    public List<WebServiceDetectionunitid> getAllUnitId();
    public List<WebServiceDetectionunitid> getDevicesList();
    public List<WebServiceTaskresult> getFailedTasksInLast5Minutes();
}
