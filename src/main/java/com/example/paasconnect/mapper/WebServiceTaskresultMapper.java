package com.example.paasconnect.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.paasconnect.entity.WebServiceTaskresult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface WebServiceTaskresultMapper extends BaseMapper<WebServiceTaskresult> {
    List<WebServiceTaskresult> getSuccessData(String taskFileId, String startTime);
    List<WebServiceTaskresult> getTaskData(String taskFileId, String startTime, String endTime);
    List<WebServiceTaskresult> getTaskDataBydeviceId(String deviceId, String startTime, String endTime);

    Map<String, Object> getTaskDataByTaskId(String taskFileId);

    List<Map<String, Object>> gettbLogPnmsPerformanceByID();

    List<Map<String, Object>> getHourlyStats(String startTime, String endTime);
    
    List<Map<String, Object>> getTotalStats(String startTime, String endTime);
}
