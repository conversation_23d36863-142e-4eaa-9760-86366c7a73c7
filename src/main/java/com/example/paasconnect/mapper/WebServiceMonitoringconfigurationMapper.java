package com.example.paasconnect.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.paasconnect.entity.WebServiceMonitoringconfiguration;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface WebServiceMonitoringconfigurationMapper extends BaseMapper<WebServiceMonitoringconfiguration> {
    public List<WebServiceMonitoringconfiguration> queryPhone(String unitId);

    /**
     * @Description: 按分类获取告警列表中的数据
     * @param:
     * @return: java.util.List<com.example.paasconnect.entity.WebServiceMonitoringconfiguration>
     * @Author: guojian
     * @Date 2024/4/24 14:52
     */
    public List<WebServiceMonitoringconfiguration> getsendAlarmDate();

}
