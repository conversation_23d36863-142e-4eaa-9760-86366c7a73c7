package com.example.paasconnect.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

public class TbLogPnmsPerformanceDTO {
    // 忽略该字段
//    @JSONField(serialize = false)
//    private String ignoreField;
//
//    // 修改字段名称
//    @JSONField(name = "new_field_name")
//    private String oldFieldName;

    @JSONField(serialize = false)
    private String id;
    @JSONField(serialize = false)
    private String kafkaType;

    @JSONField(serialize = false)
    private String unitId;
    @JSONField(name = "unitId")
    private String pnmsUnitId;

    private String extUnitId;

    private String kpiId;

    private String kpiValue;

    private String kpiDetail;

    private String kpiName;

    private Date cllTime;

    private String cllTimeStr;

    private String unitName;

    private String interval;
}
