package com.example.paasconnect.controller;

import com.example.paasconnect.ai.IHealth;
import com.example.paasconnect.ai.IPaasConnet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
public class HealthController {
    private final static Logger log = LoggerFactory.getLogger(HealthController.class);
    // 注入对象
    @Autowired
    private Environment env;

    @Value("${paas.mysql.user}")
    private String user;

    @Value("${paas.mysql.pwd}")
    private String pwd;

    @Resource
    private IHealth health;

    /**
     * @Description:
     * @param: 心跳程序
     * @return: java.lang.String
     * @Author: g<PERSON><PERSON><PERSON>
     * @Date 2022/9/30 17:29
     */

    @GetMapping("/health")
    public String health() {
        // 读取配置
        String port = env.getProperty("server.port");
        health.health();
        return "welcome to paasConnect. mysql " + port;
    }

    @GetMapping("/alarmConverge")
    public String alarmConverge() {
        // 读取配置
        health.alarmConverge();
        return "本次执行成功";
    }


//    public static void main(String[] args) {
//        long l = System.currentTimeMillis();
//
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String dateString = formatter.format(l);
//        System.out.println(dateString);
//    }
}
