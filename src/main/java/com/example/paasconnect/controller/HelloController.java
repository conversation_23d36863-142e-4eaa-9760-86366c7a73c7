package com.example.paasconnect.controller;

import com.example.paasconnect.ai.IPaasConnet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
public class HelloController {
    private final static Logger log = LoggerFactory.getLogger(HelloController.class);
    // 注入对象
    @Autowired
    private Environment env;

    @Value("${paas.mysql.user}")
    private String user;

    @Value("${paas.mysql.pwd}")
    private String pwd;

    @Resource
    private IPaasConnet PaasConnet;

    //测试
    @GetMapping("/hello")
    public String hello() {
        // 读取配置
        String port = env.getProperty("server.port");
        return "welcome to paasConnect. mysql "+port;
    }

   /**
    * @title: executeQuery
    * @Description: 查询连接数（服务端）
    * @param data
    * <AUTHOR>
    * @date 2022/3/2 15:26
    * @return int
    */
    @PostMapping("/executeQuery")
    public String executeQuery(@RequestBody String data) {
        String coll = PaasConnet.coll(data);
        return coll;
    }

    /**
     * @title: getIpList
     * @Description: 客户端采集服务端数据进行保存
     * <AUTHOR>
     * @date 2022/3/22 11:26
     * @return java.lang.String
     */
    @GetMapping("/getIpList")
    public String getIpList(@RequestParam("vip") String vip) {
        log.info("正在启动获取vip 列表...");
        String ipList = PaasConnet.getIpList(vip);
        return ipList;
    }

    @PostMapping("/verbNoun")
    public String verbNoun(@RequestBody String data) {
        log.info("同事吧数据...{}",data);
        String ipList = PaasConnet.verbNoun(data);
        return ipList;
    }

    @PostMapping("/getSmsCode")
    public String getSmsCode(@RequestBody String data) {
        log.info("getSmsCode...{}",data);
        String ipList = PaasConnet.getSmsCode(data);
        return ipList;
    }



//    public static void main(String[] args) {
//        long l = System.currentTimeMillis();
//
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String dateString = formatter.format(l);
//        System.out.println(dateString);
//    }
}
