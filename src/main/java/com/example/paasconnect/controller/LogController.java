package com.example.paasconnect.controller;

import com.example.paasconnect.util.LogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日志管理控制器
 * 提供日志查询和管理功能
 */
@RestController
@RequestMapping("/api/logs")
public class LogController {
    private static final Logger logger = LoggerFactory.getLogger(LogController.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final int DEFAULT_MAX_LINES = 1000;
    
    /**
     * 获取最新的日志内容
     * @param maxLines 最大行数
     * @return 日志内容
     */
    @GetMapping("/latest")
    public Map<String, Object> getLatestLogs(@RequestParam(value = "maxLines", defaultValue = "1000") int maxLines) {
        logger.info("获取最新日志，最大行数: {}", maxLines);
        
        String logFile = LogUtil.getCurrentLogFile();
        List<String> logLines = LogUtil.readLogFile(logFile, Math.min(maxLines, DEFAULT_MAX_LINES));
        
        Map<String, Object> result = new HashMap<>();
        result.put("file", logFile);
        result.put("lines", logLines.size());
        result.put("content", logLines);
        
        return result;
    }
    
    /**
     * 获取指定日期的日志内容
     * @param date 日期，格式为 yyyy-MM-dd
     * @param maxLines 最大行数
     * @return 日志内容
     */
    @GetMapping("/by-date")
    public Map<String, Object> getLogsByDate(
            @RequestParam("date") String date,
            @RequestParam(value = "maxLines", defaultValue = "1000") int maxLines) {
        logger.info("获取日期 {} 的日志，最大行数: {}", date, maxLines);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            LocalDate logDate = LocalDate.parse(date, DATE_FORMATTER);
            String logFile = LogUtil.getLogFileByDate(logDate);
            List<String> logLines = LogUtil.readLogFile(logFile, Math.min(maxLines, DEFAULT_MAX_LINES));
            
            result.put("file", logFile);
            result.put("date", date);
            result.put("lines", logLines.size());
            result.put("content", logLines);
        } catch (DateTimeParseException e) {
            logger.error("日期格式错误: {}", date, e);
            result.put("error", "日期格式错误，正确格式为 yyyy-MM-dd");
        }
        
        return result;
    }
    
    /**
     * 获取可用的日志文件列表
     * @return 日志文件列表
     */
    @GetMapping("/list")
    public Map<String, Object> getLogFileList() {
        logger.info("获取日志文件列表");
        
        List<String> logFiles = LogUtil.getAvailableLogFiles();
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", logFiles.size());
        result.put("files", logFiles);
        
        return result;
    }
    
    /**
     * 手动触发日志清理
     * @param days 保留天数，默认为 30 天
     * @return 清理结果
     */
    @GetMapping("/cleanup")
    public Map<String, Object> cleanupLogs(@RequestParam(value = "days", defaultValue = "30") int days) {
        logger.info("手动触发日志清理，保留天数: {}", days);
        
        LogUtil.cleanupOldLogFiles(days);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "日志清理任务已执行，保留最近 " + days + " 天的日志");
        
        return result;
    }
} 