package com.example.paasconnect.app;/**
 * <AUTHOR>
 * @function com.example.paasconnect.app
 * @date 2022/3/21 15:48
 */

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.paasconnect.ai.ISaveData;
import com.example.paasconnect.entity.PaasTbConnectionData;
import com.example.paasconnect.mapper.PaasTbConnectionDataMapper;
import org.springframework.stereotype.Service;

/**
 * @ClassName SaveDataImpl
 * @Deacription TODO
 * <AUTHOR>
 * @Date 2022/3/21 15:48
 * @Version 1.0
 **/
@Service
public class SaveDataImpl extends ServiceImpl<PaasTbConnectionDataMapper, PaasTbConnectionData> implements ISaveData {
}
