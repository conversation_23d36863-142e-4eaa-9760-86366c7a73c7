package com.example.paasconnect.app;

import com.alibaba.druid.util.StringUtils;
import com.example.paasconnect.entity.*;
import com.example.paasconnect.mapper.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class AlarmService {

    private final static Logger log = LoggerFactory.getLogger(AlarmService.class);

    // 存储已处理的任务ID和处理时间
    private static final Map<String, Long> processedTasks = new ConcurrentHashMap<>();
    // 任务处理有效期（毫秒），默认30分钟
    private static final long TASK_EXPIRY_TIME = 30 * 60 * 1000;
    // 存储任务的连续失败计数
    private static final Map<String, Integer> taskFailureCount = new ConcurrentHashMap<>();
    // 存储任务的最后一次失败原因
    private static final Map<String, String> taskLastFailureReason = new ConcurrentHashMap<>();
    // 存储任务的最后处理时间
    private static final Map<String, Long> taskLastProcessTime = new ConcurrentHashMap<>();

    @Resource
    private TbLogSendAlarmMapper tbLogSendAlarmMapper;

    @Resource
    private TbLogSendAlarmNowMapper tbLogSendAlarmNowMapper;

    @Resource
    private TbLogSendAlarmHistoryMapper tbLogSendAlarmHistoryMapper;

    @Resource
    private WebServiceMonitoringconfigurationMapper webserviceMonitoringconfigurationMapper;

    @Resource
    private WebServiceTaskresultMapper webServiceTaskresultMapper;

    @Resource
    private WebServiceDetectionunitidMapper webserviceDetectionunitidMapper;

    @Resource
    private TbLogAlarmConvergeMapper tbLogAlarmConvergeMapper;

    @Resource
    private TbLogAlarmDenyMapper tbLogAlarmDenyMapper;

    @Resource
    private TbSmsTemplateMapper tbSmsTemplateMapper;

    @Resource
    AlarmHandler alarmHandler;

    @Autowired
    private Environment env;

    private static final String CONVERGE_ID_AAAA1 = "aaaa1";
    private static final String CONVERGE_ID_AAAA2 = "aaaa2";
    private static final String CONVERGE_ID_AAAA3 = "aaaa3";
    private static final String BAM = "BAM";
    private static final String BUSINESS_MONITORING_PC_DETECTION = "【统一业务监控-PC探测】";
    private static final String CLEAR_ALARM_TAG = "[清除告警]";
    private static final String BUSINESS_MONITORING_MOBILE_DETECTION = "【统一业务监控-手机探测】";
    private static final String UNRECOVERED_ALARM_TYPE = "未恢复";

    public void alarmConverge() {
        log.info("告警收敛开始执行");
        successMessage();

        // 优化睡眠中断处理
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            log.error("线程被中断", e);
            Thread.currentThread().interrupt(); // 恢复中断状态
            return; // 退出方法
        }

        List<WebServiceMonitoringconfiguration> webServiceMonitoringconfigurations = webserviceMonitoringconfigurationMapper
                .getsendAlarmDate();
        if (webServiceMonitoringconfigurations.isEmpty()) {
            log.info("本次告警待发送数据为空");
            return;
        }

        webServiceMonitoringconfigurations.forEach(this::processConfiguration);
    }

    private void processConfiguration(WebServiceMonitoringconfiguration configuration) {
        if (configuration.getUnitId().startsWith("210-")) {
            // handlePcAlarm(configuration);
        } else {
            handleMobileAlarm(configuration);
            log.info("获取告警内容{}", configuration.getUnitId());
        }
    }

    private void handlePcAlarm(WebServiceMonitoringconfiguration configuration) {
        Map<String, Object> sendalarm = new HashMap<>();
        sendalarm.put("UNIT_ID", configuration.getUnitId());
        List<TbLogSendAlarm> tbLogSendAlarms = tbLogSendAlarmMapper.selectByMap(sendalarm);
        if (!tbLogSendAlarms.isEmpty()) {
            // 如果告警未恢复记录到异常表中
            TbLogSendAlarmNow now = new TbLogSendAlarmNow();
            TbLogSendAlarm alarm = tbLogSendAlarms.get(0);
            String eventTitle = updateEventTitle(alarm.getEventTitle(), alarm.getKpiValue());
            alarm.setEventTitle(eventTitle);
            BeanUtils.copyProperties(alarm, now);
            tbLogSendAlarmNowMapper.insert(now);
            tbLogSendAlarmMapper.deleteByMap(sendalarm);
        }
    }

    private void handleMobileAlarm(WebServiceMonitoringconfiguration configuration) {
        String convergeId = configuration.getConvergeId();
        if (CONVERGE_ID_AAAA1.equals(convergeId)) {
            proactiveDiscovery(configuration.getUnitId());
        } else if (CONVERGE_ID_AAAA2.equals(convergeId)) {
            convergingExceptionCount(configuration.getUnitId());
        } else if (CONVERGE_ID_AAAA3.equals(convergeId)) {
            convergingTimeWindow(configuration.getUnitId());
        } else {
            convergingTimeWindow(configuration.getUnitId());
        }
    }

    private String updateEventTitle(String eventTitle, String kpiValue) {
        if ("0".equals(kpiValue)) {
            return eventTitle.replace(BAM, BUSINESS_MONITORING_PC_DETECTION);
        } else {
            return eventTitle.replace(BAM, BUSINESS_MONITORING_PC_DETECTION + CLEAR_ALARM_TAG);
        }
    }

    /**
     * @Description: 告警恢复确认(判断已发生的告警)- 手机探测没有恢复短信，需要自行去判断； pc探测带恢复短信，无需特殊处理
     * @param: unitId
     * @return: java.lang.String
     * @Author: guojian
     * @Date 2024/4/19 11:31
     */
    private void successMessage() {
        List<TbLogAlarmDeny> tbLogAlarmDenies = tbLogAlarmDenyMapper.selectByMap(new HashMap<>());
        log.info("告警未恢复告警数：" + tbLogAlarmDenies.size());
        if (tbLogAlarmDenies.size() > 0) {
            for (TbLogAlarmDeny t : tbLogAlarmDenies) {
                if (BAM.equals(t.getUnitName())) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("UNIT_ID", t.getUnitId());
                    map.put("KPI_VALUE", "1");
                    List<TbLogSendAlarm> tbLogSendAlarms = tbLogSendAlarmMapper.selectByMap(map);
                    if (tbLogSendAlarms.size() > 0) {
                        // 如果恢复， 清空短信
                        Map<String, Object> map1 = new HashMap<>();
                        map.put("UNIT_ID", t.getUnitId());
                        tbLogSendAlarmNowMapper.deleteByMap(map1);
                        tbLogSendAlarmMapper.deleteByMap(map1);

                        TbLogSendAlarm tbLogSendAlarm = tbLogSendAlarms.get(0);
                        TbLogSendAlarmNow now = new TbLogSendAlarmNow();
                        String eventTitle = updateEventTitle(tbLogSendAlarm.getEventTitle(), "1");
                        BeanUtils.copyProperties(tbLogSendAlarm, now);
                        now.setEventTitle(eventTitle);
                        now.setSendTime(getDateDay());
                        tbLogSendAlarmNowMapper.insert(now);
                        tbLogAlarmDenyMapper.deleteById(t.getId());
                        log.info("本次恢复告警：：" + eventTitle);

                    }
                } else {
                    // 获取30分钟之前的到现在最新的探测结果--手机探测 + 新pc探测
                    String text = "";
                    List<WebServiceTaskresult> successData = getTaskResult(t.getUnitId());
                    log.info("UnitId ：" + t.getUnitId() + " 查询成功次数" + successData.size());
                    // 数据存在证明已经恢复，需要判断是否生成告警，如生成告警则恢复短信发送，如未生成告警，则无需发送，最后重置短信表数据
                    if (successData.size() > 0) {
                        Map<String, Object> mapHis = new HashMap<>();
                        mapHis.put("UNIT_ID", t.getUnitId());
                        // 清空待发送表数据
                        tbLogSendAlarmNowMapper.deleteByMap(mapHis);
                        tbLogSendAlarmMapper.deleteByMap(mapHis);
                        text = getSmsTemlate(t.getUnitId(), t.getUnitName(), "0");
                        // 插入恢复告警
                        TbLogSendAlarmNow now = new TbLogSendAlarmNow();
                        now.setId(create());
                        now.setEventId(t.getAlarmId());
                        now.setUnitId(t.getUnitId());
                        now.setKpiId(null);
                        now.setDataTime(getDateTime(0));
                        now.setRegionCode(null);
                        now.setEventTitle(text);
                        now.setSendFlag("1");
                        now.setSendTime(getDateDay());
                        tbLogSendAlarmNowMapper.insert(now);
                        tbLogAlarmDenyMapper.deleteById(t.getId());
                        log.info("本次恢复告警：：" + text);
                    }
                }
            }
        }
    }

    private List<WebServiceTaskresult> getTaskResult(String unitId) {
        List<WebServiceTaskresult> successData = new ArrayList<>();
        String time = getDateTime(5);
        successData = webServiceTaskresultMapper.getSuccessData(unitId, time);
        return successData;
    }

    private String getDateTime(int beginTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (beginTime == 0) {
            return sdf.format(new Date());
        } else {
            LocalDateTime currentTime = LocalDateTime.now();
            LocalDateTime time30MinutesAgo = currentTime.minusMinutes(beginTime);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            log.info("Current Time: " + currentTime.format(formatter));
            log.info("Time 30 minutes ago: " + time30MinutesAgo.format(formatter));
            return time30MinutesAgo.format(formatter);
        }
    }

    private void proactiveDiscovery(String unitId) {
        sendAlarm(unitId);
    }

    private void sendAlarm(String unitId) {
        Map<String, Object> sendalarm = new HashMap<String, Object>();
        sendalarm.put("UNIT_ID", unitId);
        List<TbLogSendAlarm> tbLogSendAlarms = tbLogSendAlarmMapper.selectByMap(sendalarm);
        if (tbLogSendAlarms.size() > 0) {
            boolean ty = false;
            String taskFile = "";
            WebServiceDetectionunitid webServiceDetectionunitids = webserviceDetectionunitidMapper.getUnitId(unitId);
            log.info("getUnitId 返回值: {}", webServiceDetectionunitids);
            if (webServiceDetectionunitids != null && !StringUtils.isEmpty(webServiceDetectionunitids.getId())) {
                ty = true;
                taskFile = webServiceDetectionunitids.getTaskFile();
                // 进行其他处理
            } else {
                // 处理未找到的情况
                log.warn("未找到 unitId: {}", unitId);
            }
            // 如果 告警内容包含，[内部告警] 则只发内部人员
            if (tbLogSendAlarms.get(0).getEventTitle().contains("[内部告警]")) {
                String alertMessage = tbLogSendAlarms.get(0).getEventTitle();
                String internalPhonesConfig = env.getProperty("alarm.internal.phones", "15510201092"); // 从配置读取
                String[] phoneArray = internalPhonesConfig.split(",");
                
                for (String phone : phoneArray) {
                    if (phone != null && !phone.trim().isEmpty()) {
                        alarmHandler.getMessageforduanxin(phone.trim(), alertMessage);
                    }
                }
            } else {
                // 正常告警处理逻辑
                log.info("稽核状态" + ty);
                TbLogSendAlarmNow now = new TbLogSendAlarmNow();
                if (ty) {
                    // 告警待回复表没有数据，需要插入
                    Map<String, Object> denySelect = new HashMap<String, Object>();
                    denySelect.put("unit_id", unitId);
                    List<TbLogAlarmDeny> tbLogAlarmDenies = tbLogAlarmDenyMapper.selectByMap(denySelect);
                    if (tbLogAlarmDenies.size() == 0) {
                        TbLogAlarmDeny deny = new TbLogAlarmDeny();
                        deny.setId(create());
                        deny.setUnitId(unitId);
                        deny.setAlarmId(tbLogSendAlarms.get(0).getEventId());
                        deny.setAlarmType(UNRECOVERED_ALARM_TYPE);
                        log.info("告警未回复表插入数据{}{}：", taskFile, unitId);
                        if (!StringUtils.isEmpty(taskFile)) {
                            // 字符串截取
                            taskFile = taskFile.substring(0, taskFile.length() - 3);
                            deny.setUnitName(taskFile);
                        }
                        tbLogAlarmDenyMapper.insert(deny);
                    }

                }

                now.setId(create());
                now.setEventId(tbLogSendAlarms.get(0).getEventId());
                now.setUnitId(unitId);
                now.setKpiId(null);
                now.setDataTime(getDateTime(0));
                now.setRegionCode(null);
                now.setEventTitle(getSmsTemlate(unitId, tbLogSendAlarms.get(0).getEventTitle(), "1"));
                now.setSendFlag("1");
                now.setSendTime(getDateDay());    // 判断业务列表是否存在，如不存在不是正常业务
                tbLogSendAlarmNowMapper.insert(now);
            }
            tbLogSendAlarmMapper.deleteByMap(sendalarm);
        }
    }

    private String getDateDay() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(new Date());
    }

    // private String updateAlarm(String message, String unitId) {
    // String newMessage = "";
    // if (unitId.startsWith("710-01")) {
    // newMessage = BUSINESS_MONITORING_PC_DETECTION + "[普通告警]" + message;
    // } else {
    // if (message.contains("超过")) {
    // newMessage = BUSINESS_MONITORING_MOBILE_DETECTION + "[一般告警]" + message;
    // } else if (message.contains("探测结束")) {
    // newMessage = BUSINESS_MONITORING_MOBILE_DETECTION + "[普通告警]" + message;
    // } else {
    // newMessage = BUSINESS_MONITORING_MOBILE_DETECTION + "[重要告警]" + message;
    // }
    // }
    // return newMessage;
    // }

    /**
     * @Description: 异常次数收敛
     * @param:
     * @return: boolean
     * @Author: guojian
     * @Date 2024/4/23 17:30
     */
    private void convergingExceptionCount(String unitId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("name", "异常次数收敛");
        List<TbLogAlarmConverge> tbLogAlarmConverges = tbLogAlarmConvergeMapper.selectByMap(map);
        String rule = tbLogAlarmConverges.get(0).getRule().trim();
        int alarmRule = Integer.parseInt(rule);
        // 获取收敛阈值
        Map<String, Object> sendalarm = new HashMap<String, Object>();
        sendalarm.put("UNIT_ID", unitId);
        List<TbLogSendAlarm> tbLogSendAlarms = tbLogSendAlarmMapper.selectByMap(sendalarm);
        log.info("异常次数收敛阈值：" + alarmRule + "，待发送告警数量：" + tbLogSendAlarms.size());
        if (tbLogSendAlarms.size() >= alarmRule) {
            // 如果待发送告警数量高于阈值，则进行告警发送
            sendAlarm(unitId);
        }
    }

    /**
     * @Description: 时间窗口收敛
     * @param:
     * @return: boolean
     * @Author: guojian
     * @Date 2024/4/23 17:31
     */
    private void convergingTimeWindow(String unitId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("name", "时间窗口收敛");
        List<TbLogAlarmConverge> tbLogAlarmConverges = tbLogAlarmConvergeMapper.selectByMap(map);
        String rule = tbLogAlarmConverges.get(0).getRule().trim();
        int alarmRule = 0;
        if ("$".equals(rule)) {
            alarmRule = -1;
        } else {
            alarmRule = Integer.parseInt(rule);
        }
        // 获取收敛阈值
        Map<String, Object> sendalarm = new HashMap<String, Object>();
        sendalarm.put("UNIT_ID", unitId);
        List<TbLogSendAlarm> tbLogSendAlarms = tbLogSendAlarmMapper.selectByMap(sendalarm);
        log.info("时间窗口收敛阈值：" + alarmRule + "，待发送告警数量：" + tbLogSendAlarms.size());
        if (alarmRule == -1) {
            // 如果阈值为-1，则根据时间窗口发送,默认只发送一次
            Map<String, Object> denySelect = new HashMap<String, Object>();
            denySelect.put("unit_id", unitId);
            List<TbLogAlarmDeny> tbLogAlarmDenies = tbLogAlarmDenyMapper.selectByMap(denySelect);
            if (tbLogAlarmDenies.size() > 0) {
                log.info("该告警未恢复，不发送告警");
                // 清空告警内容
                tbLogSendAlarmMapper.deleteByMap(sendalarm);
            } else {
                if (tbLogSendAlarms.size() > 1) {
                    log.info("达到时间窗口收敛：发送告警1条");
                    sendAlarm(unitId);
                } else {
                    // 查找5分钟之前的结果
                    List<WebServiceTaskresult> successData = getTaskResult(unitId);
                    if (successData.size() > 0) {
                        // 清空要发送的短信
                        log.info("未发送告警检测业务已恢复，删除即将发送的告警");
                        int i = tbLogSendAlarmNowMapper.deleteByMap(sendalarm);
                        int i1 = tbLogSendAlarmMapper.deleteByMap(sendalarm);
                        log.info("删除告警数量：" + i + "，" + i1);
                    }
                }
                // log.info("达到时间窗口收敛：发送告警1条");
                // sendAlarm(unitId);
            }
        } else {
            int minTime = tbLogSendAlarms.size() * 15; // 15分钟探测一次
            // 计算出探测的周期时间，如果探测周期大于阈值，则进行告警发送
            if (minTime > alarmRule) {
                // 如果待发送告警数量高于阈值，则进行告警发送
                sendAlarm(unitId);
            }
        }
    }

    /**
     * 验证告警逻辑
     */
    public void VerifyTheAlarm() {
        // 获取近3分钟失败的数据
        List<WebServiceTaskresult> allUnitId = webserviceDetectionunitidMapper.getFailedTasksInLast5Minutes();
        log.info("本次发现探测当前探测失败业务数：+ " + allUnitId.size());

        // 使用当前时间作为查询基准点
        String currentTime = getDateTime(0);
        String queryTime = getDateTime(60);
        log.info("当前时间: " + currentTime + ", 查询时间范围: " + queryTime + " 到 " + currentTime);

        // 清理过期的任务记录
        cleanExpiredTasks();

        if (allUnitId.isEmpty()) {
            return;
        }

        // 处理每个失败的任务
        for (WebServiceTaskresult taskResult : allUnitId) {
            String unitIdKey = taskResult.getTaskFileId();

            // 查询最近时间内的探测结果
            List<WebServiceTaskresult> taskData = webServiceTaskresultMapper.getTaskData(unitIdKey, queryTime,
                    currentTime);

            // 如果没有数据，跳过处理
            if (taskData.isEmpty()) {
                log.info("任务 " + unitIdKey + " 没有查询到数据，跳过处理");
                continue;
            }

            // 获取最新的任务结果
            WebServiceTaskresult latestResult = taskData.get(0);
            String latestTaskKey = unitIdKey + "_" + latestResult.getStartTime();

            // 检查是否已经处理过该任务的最新结果
            if (processedTasks.containsKey(latestTaskKey)) {
                log.info("任务已处理过，跳过: " + latestTaskKey);
                continue;
            }

            // 标记当前任务已处理
            processedTasks.put(latestTaskKey, System.currentTimeMillis());
            taskLastProcessTime.put(unitIdKey, System.currentTimeMillis());

            // 根据unitId前缀区分处理逻辑
            if (unitIdKey.startsWith("710-")) {
                // 710-开头的unitId使用原来的逻辑
                process710Task(unitIdKey, latestResult, currentTime);
            } else {
                // 非710-开头的unitId使用新逻辑："一个成功则成功"
                processNon710Task(unitIdKey, taskData, latestResult, currentTime);
            }
        }
    }

    /**
     * 处理710-开头的任务（使用原逻辑）
     */
    private void process710Task(String unitIdKey, WebServiceTaskresult latestResult, String currentTime) {
        log.info("任务 " + unitIdKey + " 是710-前缀，使用原逻辑处理");
        String latestReason = latestResult.getReason();

        // 检查任务是否失败
        if ("0".equals(latestResult.getStatus())) {
            // 获取上次失败原因
            String previousReason = taskLastFailureReason.getOrDefault(unitIdKey, "");

            // 如果上次也失败了，并且原因相同，则增加失败计数
            if (!previousReason.isEmpty() && previousReason.equals(latestReason)) {
                int currentFailCount = taskFailureCount.getOrDefault(unitIdKey, 1) + 1;
                taskFailureCount.put(unitIdKey, currentFailCount);

                log.info("任务 " + unitIdKey + " 连续失败次数: " + currentFailCount + ", 当前原因: " + latestReason + ", 与上次原因相同");

                // 检查是否需要发送告警
                if (currentFailCount == 2) {
                    // 首次触发告警条件时，发送两条相同的告警
                    log.info("任务 " + unitIdKey + " 首次触发告警条件，发送2条相同告警");
                    checkAndSendAlarm(unitIdKey, latestResult, latestReason, currentTime);
                    checkAndSendAlarm(unitIdKey, latestResult, latestReason, currentTime);
                } else {
                    // 后续触发只发送一条
                    checkAndSendAlarm(unitIdKey, latestResult, latestReason, currentTime);
                }
            } else {
                // 首次失败或原因不同，记录当前失败但不发送告警
                taskFailureCount.put(unitIdKey, 1);
                log.info("任务 " + unitIdKey + " 首次失败或原因不同，记录但不告警. 当前原因: " + latestReason);
            }

            // 无论如何都更新最后失败原因
            taskLastFailureReason.put(unitIdKey, latestReason);
        } else {
            // 任务成功，重置失败计数
            resetTaskFailureStatus(unitIdKey);
        }
    }

    /**
     * 处理非710-开头的任务（使用新逻辑：一个成功则成功）
     */
    private void processNon710Task(String unitIdKey, List<WebServiceTaskresult> taskData,
            WebServiceTaskresult latestResult, String currentTime) {
        log.info("任务 " + unitIdKey + " 非710-前缀，使用新逻辑处理");
        String latestReason = latestResult.getReason();

        // 检查同一时间窗口内是否有成功的探测结果
        boolean hasSuccessResult = checkForSuccessResult(unitIdKey, taskData);

        // 如果有成功的结果，将任务视为成功，重置失败计数
        if (hasSuccessResult) {
            resetTaskFailureStatus(unitIdKey);
            return;
        }

        log.info("任务 " + unitIdKey + " 在时间窗口内全部失败，最新数据时间: " + latestResult.getStartTime());

        // 获取上次失败原因
        String previousReason = taskLastFailureReason.getOrDefault(unitIdKey, "");

        // 如果上次也失败了，并且原因相同，则增加失败计数
        if (!previousReason.isEmpty() && previousReason.equals(latestReason)) {
            int currentFailCount = taskFailureCount.getOrDefault(unitIdKey, 1) + 1;
            taskFailureCount.put(unitIdKey, currentFailCount);

            log.info("任务 " + unitIdKey + " 连续失败次数: " + currentFailCount + ", 当前原因: " + latestReason + ", 与上次原因相同");

            // 检查是否需要发送告警
            checkAndSendAlarm(unitIdKey, latestResult, latestReason, currentTime);
        } else {
            // 首次失败或原因不同，记录当前失败但不发送告警
            taskFailureCount.put(unitIdKey, 1);
            log.info("任务 " + unitIdKey + " 首次失败或原因不同，记录但不告警. 当前原因: " + latestReason);
        }

        // 无论如何都更新最后失败原因
        taskLastFailureReason.put(unitIdKey, latestReason);
    }

    /**
     * 检查同一时间窗口内是否有成功的探测结果
     */
    private boolean checkForSuccessResult(String unitIdKey, List<WebServiceTaskresult> taskData) {
        for (WebServiceTaskresult result : taskData) {
            if ("1".equals(result.getStatus())) {
                log.info("任务 " + unitIdKey + " 在时间窗口内有成功的探测结果，时间: " + result.getStartTime() + ", 终端: "
                        + result.getDeviceId());
                return true;
            }
        }
        return false;
    }

    /**
     * 重置任务失败状态
     */
    private void resetTaskFailureStatus(String unitIdKey) {
        taskFailureCount.put(unitIdKey, 0);
        taskLastFailureReason.put(unitIdKey, "");
        log.info("任务 " + unitIdKey + " 探测成功或有成功结果，重置失败计数");
    }

    /**
     * 检查是否需要发送告警并发送
     */
    private void checkAndSendAlarm(String unitIdKey, WebServiceTaskresult result, String reason, String currentTime) {
        // 检查错误信息是否包含需要过滤的关键词
        if (reason.contains("找不到") || reason.contains("lineNumber")) {
            log.info("任务 " + unitIdKey + " 错误信息包含需要过滤的关键词，不发送告警");
            // 不发送告警，但仍然更新失败计数和原因
            return;
        }

        // 连续失败且原因相同，且不包含过滤关键词，发送告警
        String message = reason;
        String alarmMessage = message + ",探测时间：" + result.getStartTime() + ",耗时：" + result.getTime() + "毫秒";

        // 发送告警-插入待发送表
        log.info("连续失败且原因相同，发送告警: " + alarmMessage);
        TbLogSendAlarm now = new TbLogSendAlarm();
        now.setId(create());
        now.setEventId(result.getTaskId());
        now.setUnitId(unitIdKey);
        now.setDataTime(currentTime);
        now.setRegionCode(null);
        now.setEventTitle(alarmMessage);
        now.setSendFlag("1");
        now.setSendTime(getDateDay());
        tbLogSendAlarmMapper.insert(now);
    }

    /**
     * 清理过期的任务记录
     */
    private void cleanExpiredTasks() {
        long currentTime = System.currentTimeMillis();

        // 清理processedTasks
        Iterator<Map.Entry<String, Long>> iterator = processedTasks.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Long> entry = iterator.next();
            if (currentTime - entry.getValue() > TASK_EXPIRY_TIME) {
                iterator.remove();
            }
        }

        // 清理过期的任务处理记录
        Iterator<Map.Entry<String, Long>> processIterator = taskLastProcessTime.entrySet().iterator();
        while (processIterator.hasNext()) {
            Map.Entry<String, Long> entry = processIterator.next();
            if (currentTime - entry.getValue() > TASK_EXPIRY_TIME) {
                processIterator.remove();

                // 同时清理相关的失败计数和原因记录
                String unitId = entry.getKey();
                taskFailureCount.remove(unitId);
                taskLastFailureReason.remove(unitId);
            }
        }

        log.info("清理过期任务后，当前缓存任务数: " + processedTasks.size() + ", 失败计数记录: " + taskFailureCount.size() + ", 处理时间记录: "
                + taskLastProcessTime.size());
    }

    /**
     * @Description: 短信模版拼接
     * @param: unitId
     * @param: alarmMessage
     * @return: java.lang.String
     * @Author: guojian
     * @Date 2024/12/7 16:00
     */
    public String getSmsTemlate(String unitId, String alarmMessage, String Type) {
        String message = "";// 告警详情
        // 【本系统】【告警级别】[业务系统] [业务场景] 告警详情 + 发生时间 [业务负责人]
        String smsTemlate = "";
        Map<String, Object> sendalarm = new HashMap<String, Object>();
        sendalarm.put("unit_id", unitId);
        List<TbSmsTemplate> tbSmsTemplates = tbSmsTemplateMapper.selectByMap(sendalarm);
        if ("0".equals(Type)) {
            // 告警恢复拼接
            if (tbSmsTemplates.size() > 0) {
                smsTemlate = "[" + tbSmsTemplates.get(0).getBusinessSystem() + "]["
                        + tbSmsTemplates.get(0).getBusinessScenarios() + "]";
            }
            if (StringUtils.isEmpty(smsTemlate)) {
                smsTemlate = alarmMessage;
            }
            if (unitId.startsWith("710-01")) {
                message = BUSINESS_MONITORING_PC_DETECTION + CLEAR_ALARM_TAG + smsTemlate + "本次探测正常，告警恢复正常，" + "探测结束";
            } else {
                message = BUSINESS_MONITORING_MOBILE_DETECTION + CLEAR_ALARM_TAG + smsTemlate + "本次探测正常，告警恢复正常，"
                        + "探测结束";
            }
        } else {
            if (tbSmsTemplates.size() > 0) {
                smsTemlate = "[" + tbSmsTemplates.get(0).getBusinessSystem() + "]["
                        + tbSmsTemplates.get(0).getBusinessScenarios() + "]" + alarmMessage + " [业务负责人："
                        + tbSmsTemplates.get(0).getBusinessOwner() + "]";
            } else {
                smsTemlate = alarmMessage;
            }
            if (unitId.startsWith("710-01")) {
                message = BUSINESS_MONITORING_PC_DETECTION + "[普通告警]" + smsTemlate;
            } else {
                if (alarmMessage.contains("超过")) {
                    message = BUSINESS_MONITORING_MOBILE_DETECTION + "[一般告警]" + smsTemlate;
                } else if (alarmMessage.contains("探测结束")) {
                    message = BUSINESS_MONITORING_MOBILE_DETECTION + "[普通告警]" + smsTemlate;
                } else {
                    message = BUSINESS_MONITORING_MOBILE_DETECTION + "[重要告警]" + smsTemlate;
                }
            }
        }
        return message;
    }

    public String create() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replaceAll("-", "");
    }
}