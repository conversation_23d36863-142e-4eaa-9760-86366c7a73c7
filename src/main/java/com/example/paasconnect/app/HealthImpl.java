package com.example.paasconnect.app;/**
 * <AUTHOR>
 * @function com.example.paasconnect.app
 * @date 2022/3/12 15:55
 */

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.example.paasconnect.ai.*;
import com.example.paasconnect.entity.*;
import com.example.paasconnect.exception.HttpClientException;
import com.example.paasconnect.mapper.*;
import com.example.paasconnect.util.HttpClientUtil;
import com.example.paasconnect.util.IVRUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @ClassName PaasConnetImpl  -- 老方法
 * @Deacription TODO
 * <AUTHOR>
 * @Date 2022/3/12 15:55
 * @Version 1.0
 **/

@Service
public class HealthImpl implements IHealth {
    private final static Logger log = LoggerFactory.getLogger(HealthImpl.class);

    @Resource
    private TbLogSendAlarmMapper tbLogSendAlarmMapper;

    @Resource
    private TbLogSendAlarmNowMapper tbLogSendAlarmNowMapper;

    @Resource
    private TbLogSendAlarmHistoryMapper tbLogSendAlarmHistoryMapper;

    @Resource
    private WebServiceMonitoringconfigurationMapper webserviceMonitoringconfigurationMapper;

    @Resource
    private WebServiceTaskresultMapper webServiceTaskresultMapper;

    @Resource
    private WebServiceDetectionunitidMapper webserviceDetectionunitidMapper;

    @Resource
    private TbLogAlarmConvergeMapper tbLogAlarmConvergeMapper;

    @Resource
    private TbLogAlarmDenyMapper tbLogAlarmDenyMapper;

    @Autowired
    private Environment env;


    /**
     * @Description: 告警表定时检查，发送告警（短信，微信消息）
     * @param:
     * @return: void
     * @Author: guojian
     * @Date 2024/4/18 15:36
     */
    @Override
    public void health() {
        try {
            Map<String, Object> alarMap = new HashMap<>();
            List<TbLogSendAlarmNow> alarm = tbLogSendAlarmNowMapper.selectByMap(alarMap);
            List<TbLogSendAlarmHistory> li = new ArrayList<>();
            if (alarm.size() > 0) {
                alarm.stream().forEach(t -> {
                    String message = t.getEventTitle();
                    List<WebServiceMonitoringconfiguration> webServiceMonitoringconfigurations = webserviceMonitoringconfigurationMapper.queryPhone(t.getUnitId());
                    String weixinPhone = "";
                    List<String> duanxinPhone = new ArrayList<>();
                    if (webServiceMonitoringconfigurations.size() > 0) {
                        for (WebServiceMonitoringconfiguration w : webServiceMonitoringconfigurations) {
                            String alarmType = w.getAlarmType().trim();
                            String phone = w.getAlarmPhone().trim();
                            //1 微信。 2 短信 3 ivr
                            if (alarmType.contains("1")) {
                                weixinPhone += phone + "|";
                            }
                            if (alarmType.contains("2")) {
                                duanxinPhone.add(phone);
                            }
                            if (alarmType.contains("3")) {
                                IVRUtils.sendIvrAlarm(phone, message);
                            }
                        }
                    }
                    boolean post1 = true;
                    boolean post2 = true;
                    if (duanxinPhone.size() > 0) {
                        log.info("短信发送开始调用");
                        for (String s : duanxinPhone) {
                            post1 = getMessageforduanxin(s, message);
                        }
                    }
                    if (weixinPhone.length() > 0) {
                        log.info("企微发送开始调用");
                        post2 = getMessageforweixin(weixinPhone.substring(0, weixinPhone.length() - 1), message);
                    }
                    if (post1 && post2) {
                        TbLogSendAlarmHistory history = new TbLogSendAlarmHistory();
                        history.setId(t.getId()+System.currentTimeMillis());
                        history.setEventId(t.getEventId());
                        history.setUnitId(t.getUnitId());
                        history.setKpiId(t.getKpiId());
                        history.setDataTime(t.getDataTime());
                        history.setRegionCode(t.getRegionCode());
                        history.setEventTitle(t.getEventTitle());
                        history.setKpiValue(t.getKpiValue());
                        history.setKbpClass(t.getKbpClass());
                        history.setGenerantTime(t.getGenerantTime());
                        history.setConfirmTime(t.getConfirmTime());
                        history.setActionState(t.getActionState());
                        history.setSendTime(t.getSendTime());
                        history.setSendFlag("0");
                        tbLogSendAlarmHistoryMapper.insert(history);
                        tbLogSendAlarmNowMapper.deleteById(t.getId());
                    }
// else {
//                        log.error("能开发送异常；");
//                        TbLogSendAlarmHistory history = new TbLogSendAlarmHistory();
//                        history.setId(t.getId()+System.currentTimeMillis());
//                        history.setEventId(t.getEventId());
//                        history.setUnitId(t.getUnitId());
//                        history.setKpiId(t.getKpiId());
//                        history.setDataTime(t.getDataTime());
//                        history.setRegionCode(t.getRegionCode());
//                        history.setEventTitle(t.getEventTitle());
//                        history.setKpiValue(t.getKpiValue());
//                        history.setKbpClass("发送状态异常");
//                        history.setGenerantTime(t.getGenerantTime());
//                        history.setConfirmTime(t.getConfirmTime());
//                        history.setActionState(t.getActionState());
//                        history.setSendTime(t.getSendTime());
//                        history.setSendFlag("");
//                        tbLogSendAlarmHistoryMapper.insert(history);
//                        tbLogSendAlarmMapper.deleteById(t.getId());
//                        getMessageforduanxin("15510201092", t.getEventTitle()+"发送异常，状态码异常!");
//                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("health error：", e);
        }
    }

    /**
     * @Description: 告警收敛，主要处理短信发送，告警恢复，根据告警收敛规则，发送对应短信
     * @param:
     * @return: void
     * @Author: guojian
     * @Date 2024/4/18 15:37
     */

    @Override
    public void alarmConverge() {
        //执行之前先去上一次的告警数据是否恢复；
        log.info("告警收敛开始执行");
        successMessage();
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        List<WebServiceMonitoringconfiguration> webServiceMonitoringconfigurations = webserviceMonitoringconfigurationMapper.getsendAlarmDate();
        if (webServiceMonitoringconfigurations.size() > 0) {
            for (WebServiceMonitoringconfiguration w : webServiceMonitoringconfigurations) {
                if(w.getUnitId().startsWith("210-")){
                    //判断是PC的告警还是手机侧告警，210-开头为PC告警
                    Map<String, Object> sendalarm = new HashMap<String, Object>();
                    sendalarm.put("UNIT_ID", w.getUnitId());
                    List<TbLogSendAlarm> tbLogSendAlarms = tbLogSendAlarmMapper.selectByMap(sendalarm);
                    TbLogSendAlarmNow Now = new TbLogSendAlarmNow();
                    if (tbLogSendAlarms.size() > 0) {
                        String eventTitle = tbLogSendAlarms.get(0).getEventTitle();
                        String kpiValue = tbLogSendAlarms.get(0).getKpiValue();
                        String bam = "";
                        if("0".equals(kpiValue)){
                            bam = eventTitle.replace("BAM", "【业务监控-PC探测】");
                        }else{
                            bam = eventTitle.replace("BAM", "【业务监控-PC探测】[清除告警]");
                        }
                        tbLogSendAlarms.get(0).setEventTitle(bam);
                        BeanUtils.copyProperties(tbLogSendAlarms.get(0), Now);
                        tbLogSendAlarmNowMapper.insert(Now);
                        tbLogSendAlarmMapper.deleteByMap(sendalarm);
                    }
                }else{
                    //判断未发送的告警是否已经恢复
                    if ("aaaa1".equals(w.getConvergeId())) {
                        proactiveDiscovery(w.getUnitId());
                    } else if ("aaaa2".equals(w.getConvergeId())) {
                        //异常次数收敛
                        convergingExceptionCount(w.getUnitId());
                    } else if ("aaaa3".equals(w.getConvergeId())) {
                        convergingTimeWindow(w.getUnitId());
                    } else {
                        convergingExceptionCount(w.getUnitId());
                    }
                }
            }
        } else {
            log.info("本次告警待发送数据为空");
        }
    }

    /**
     * @Description: 异常次数收敛
     * @param:
     * @return: boolean
     * @Author: guojian
     * @Date 2024/4/23 17:30
     */
    private void convergingExceptionCount(String unitId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("name", "异常次数收敛");
        List<TbLogAlarmConverge> tbLogAlarmConverges = tbLogAlarmConvergeMapper.selectByMap(map);
        String rule = tbLogAlarmConverges.get(0).getRule().trim();
        int alarmRule = Integer.parseInt(rule);
        //获取收敛阈值
        Map<String, Object> sendalarm = new HashMap<String, Object>();
        sendalarm.put("UNIT_ID", unitId);
        List<TbLogSendAlarm> tbLogSendAlarms = tbLogSendAlarmMapper.selectByMap(sendalarm);
        log.info("异常次数收敛阈值：" + alarmRule + "，待发送告警数量：" + tbLogSendAlarms.size());
        if (tbLogSendAlarms.size() >= alarmRule) {
            //如果待发送告警数量高于阈值，则进行告警发送
            sendAlarm(unitId);
        }
    }

    /**
     * @Description: 时间窗口收敛
     * @param:
     * @return: boolean
     * @Author: guojian
     * @Date 2024/4/23 17:31
     */
    private void convergingTimeWindow(String unitId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("name", "时间窗口收敛");
        List<TbLogAlarmConverge> tbLogAlarmConverges = tbLogAlarmConvergeMapper.selectByMap(map);
        String rule = tbLogAlarmConverges.get(0).getRule().trim();
        int alarmRule = 0;
        if ("$".equals(rule)) {
            alarmRule = -1;
        } else {
            alarmRule = Integer.parseInt(rule);
        }
        //获取收敛阈值
        Map<String, Object> sendalarm = new HashMap<String, Object>();
        sendalarm.put("UNIT_ID", unitId);
        List<TbLogSendAlarm> tbLogSendAlarms = tbLogSendAlarmMapper.selectByMap(sendalarm);
        log.info("时间窗口收敛阈值：" + alarmRule + "，待发送告警数量：" + tbLogSendAlarms.size());
        if (alarmRule == -1) {
            //如果阈值为-1，则根据时间窗口发送,默认只发送一次
            Map<String, Object> denySelect = new HashMap<String, Object>();
            denySelect.put("unit_id", unitId);
            List<TbLogAlarmDeny> tbLogAlarmDenies = tbLogAlarmDenyMapper.selectByMap(denySelect);
            if (tbLogAlarmDenies.size() > 0) {
                log.info("该告警未恢复，不发送告警");
                //清空告警内容
                tbLogSendAlarmMapper.deleteByMap(sendalarm);
            } else {
//                if(tbLogSendAlarms.size() > 3){
                    log.info("达到时间窗口收敛：发送告警1条");
                    sendAlarm(unitId);
//                }
            }
        } else {
            int minTime = tbLogSendAlarms.size() * 15; //15分钟探测一次
            //计算出探测的周期时间，如果探测周期大于阈值，则进行告警发送
            if (minTime > alarmRule) {
                //如果待发送告警数量高于阈值，则进行告警发送
                sendAlarm(unitId);
            }
        }
    }

    /**
     * @Description: 主动发送（默认收到即发送）
     * @param:
     * @return: boolean
     * @Author: guojian
     * @Date 2024/4/23 17:31
     */
    private void proactiveDiscovery(String unitId) {
        sendAlarm(unitId);
    }

    private String getDateTime(int beginTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (beginTime == 0) {
            return sdf.format(new Date());
        } else {
            LocalDateTime currentTime = LocalDateTime.now();
            LocalDateTime time30MinutesAgo = currentTime.minusMinutes(beginTime);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            log.info("Current Time: " + currentTime.format(formatter));
            log.info("Time 30 minutes ago: " + time30MinutesAgo.format(formatter));
            return time30MinutesAgo.format(formatter);
        }
    }

    private void sendAlarm(String unitId) {
        Map<String, Object> sendalarm = new HashMap<String, Object>();
        sendalarm.put("UNIT_ID", unitId);
        List<TbLogSendAlarm> tbLogSendAlarms = tbLogSendAlarmMapper.selectByMap(sendalarm);
        if (tbLogSendAlarms.size() > 0) {
            //判断业务列表是否存在，如不存在不是正常业务
            WebServiceDetectionunitid webServiceDetectionunitids = webserviceDetectionunitidMapper.getUnitId(unitId);
            if(webServiceDetectionunitids == null){
                log.info("该业务不存在，不记录告警");
            }else{
                //告警待回复表没有数据，需要插入
                Map<String, Object> denySelect = new HashMap<String, Object>();
                denySelect.put("unit_id", unitId);
                List<TbLogAlarmDeny> tbLogAlarmDenies = tbLogAlarmDenyMapper.selectByMap(denySelect);
                if (tbLogAlarmDenies.size() == 0) {
                    TbLogAlarmDeny deny = new TbLogAlarmDeny();
                    deny.setId(create());
                    deny.setUnitId(unitId);
                    deny.setAlarmId(tbLogSendAlarms.get(0).getEventId());
                    deny.setAlarmType("未恢复");
                    log.info("告警未回复表插入数据{}{}：" ,webServiceDetectionunitids.getTaskFile(),unitId);
                    if (!StringUtils.isEmpty(webServiceDetectionunitids.getTaskFile())) {
                        String taskFile = webServiceDetectionunitids.getTaskFile();
                        //字符串截取
                        taskFile = taskFile.substring(0, taskFile.length() - 3);
                        deny.setUnitName(taskFile);
                    }
                    tbLogAlarmDenyMapper.insert(deny);
                }

            }
            TbLogSendAlarmNow now = new TbLogSendAlarmNow();
            now.setId(create());
            now.setEventId(tbLogSendAlarms.get(0).getEventId());
            now.setUnitId(unitId);
            now.setKpiId(null);
            now.setDataTime(getDateTime(0));
            now.setRegionCode(null);
            now.setEventTitle(updateAlarm(tbLogSendAlarms.get(0).getEventTitle()));
            now.setSendFlag("1");
            now.setSendTime(getDateDay());
            tbLogSendAlarmNowMapper.insert(now);
            tbLogSendAlarmMapper.deleteByMap(sendalarm);
        }
    }

    private String getDateDay() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(new Date());
    }


    /**
     * @Description: 根据短信内容，优化短信的展示形式分类；普通告警；重要告警；严重告警
     * 超时：普通告警：获取“超过”
     * 失败：重要告警：获取“探测结束”
     * 插件：严重告警：else
     * @param: message
     * @return: java.lang.String
     * @Author: guojian
     * @Date 2024/4/18 15:39
     */
    private String updateAlarm(String message) {
        String newMessage = "";
        if (message.contains("超过")) {
            newMessage = "【业务监控-手机探测】[一般告警]" + message;
        } else if (message.contains("探测结束")) {
            newMessage = "【业务监控-手机探测】[普通告警]" + message;
        } else {
            newMessage = "【业务监控-手机探测】[重要告警]" + message;
        }
        return newMessage;
    }

    /**
     * @Description: 告警恢复确认(判断已发生的告警)- 手机探测没有恢复短信，需要自行去判断； pc探测带恢复短信，无需特殊处理
     * @param: unitId
     * @return: java.lang.String
     * @Author: guojian
     * @Date 2024/4/19 11:31
     */
    private void successMessage() {
        List<TbLogAlarmDeny> tbLogAlarmDenies = tbLogAlarmDenyMapper.selectByMap(new HashMap<>());
        log.info("告警未恢复告警数：" + tbLogAlarmDenies.size());
        if (tbLogAlarmDenies.size() > 0) {
            for (TbLogAlarmDeny t : tbLogAlarmDenies) {
                //获取30分钟之前的到现在最新的探测结果
                String text = "";
                String time = getDateTime(30);
                List<WebServiceTaskresult> successData = webServiceTaskresultMapper.getSuccessData(t.getUnitId(), time);
                //数据存在证明已经恢复，需要判断是否生成告警，如生成告警则恢复短信发送，如未生成告警，则无需发送，最后重置短信表数据
                if (successData.size() > 0) {
                    Map<String, Object> mapHis = new HashMap<>();
                    mapHis.put("UNIT_ID", t.getUnitId());
                    //清空待发送表数据
                    tbLogSendAlarmNowMapper.deleteByMap(mapHis);
                    tbLogSendAlarmMapper.deleteByMap(mapHis);

                    text = "【业务监控-手机探测】[清除告警]" + t.getUnitName() + "本次探测正常，告警恢复正常，" + "探测结束";
                    //插入恢复告警
                    TbLogSendAlarmNow now = new TbLogSendAlarmNow();
                    now.setId(create());
                    now.setEventId(t.getAlarmId());
                    now.setUnitId(t.getUnitId());
                    now.setKpiId(null);
                    now.setDataTime(getDateTime(0));
                    now.setRegionCode(null);
                    now.setEventTitle(text);
                    now.setSendFlag("1");
                    now.setSendTime(getDateDay());
                    tbLogSendAlarmNowMapper.insert(now);
                    tbLogAlarmDenyMapper.deleteById(t.getId());
                    log.info("本次恢复告警：：" + text);
                }
            }
        }
    }
//    private void successMessageSendAlarm(String unitId) {
//        String time = getDateTime(15);
//        List<WebServiceTaskresult> successData = webServiceTaskresultMapper.getSuccessData(unitId, time);
//    }

    private boolean getMessageforduanxin(String phoneNumber, String txt) {
        try {
            String duanxinUrl = env.getProperty("alarm.duanxinUrl");
            JSONObject object = new JSONObject();
            object.put("ORIGIN_DOMAIN", "70");
            JSONObject messageInfo = new JSONObject();
            messageInfo.put("RECV_OBJECT", phoneNumber);
            messageInfo.put("MESSAGE_CONTENT", txt);
            object.put("MESSAGE_INFO", messageInfo);
            JSONObject obj = new JSONObject();
            obj.put("SUBMIT_MESSAGE_REQ", object);
            String post = HttpClientUtil.post(duanxinUrl, obj);
            log.info("接口入参：" + object.toJSONString());
            log.info("接口出参：" + post);
            JSONObject jsonObject = JSONObject.parseObject(post);
            JSONArray uni_osp_body = jsonObject.getJSONObject("UNI_OSP_BODY").getJSONArray("SUBMIT_MESSAGE_RSP");
            String result = uni_osp_body.getJSONObject(0).getString("RSP_CODE");
            if (result.contains("0")) {
                return true;
            }
        } catch (HttpClientException e) {
            e.printStackTrace();
        }
        return false;
    }

    private boolean getMessageforweixin(String phoneNumber, String txt) {
        try {
            String weixinUrl = env.getProperty("alarm.weixinUrl");
            JSONObject object = new JSONObject();
            object.put("phoneNumber", phoneNumber);
            object.put("result", txt);
            String post = HttpClientUtil.post(weixinUrl, object);
            log.info("接口入参：" + object.toJSONString());
            log.info("接口出参：" + post);
            JSONObject jsonObject = JSONObject.parseObject(post);
            String result = jsonObject.getJSONObject("UNI_OSP_BODY").getString("retCode");
            if (result.contains("0")) {
                return true;
            }

        } catch (HttpClientException e) {
            e.printStackTrace();
        }
        return false;
    }
    public String create() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replaceAll("-", "");
    }

//
//    public static void main(String[] args) {
//        JSONObject jsonObject = JSONObject.parseObject("{\"UNI_OSP_BODY\":{\"SUBMIT_MESSAGE_RSP\":[{\"RSP_DESC\":\"调用成功\",\"RSP_CODE\":\"0000\"}],\"route_node\":\"ABILITY_10016000\"},\"UNI_OSP_HEAD\":{\"RESP_DESC\":\"调用成功!\",\"RESP_CODE\":\"00000\"}}");
//        JSONArray uni_osp_body = jsonObject.getJSONObject("UNI_OSP_BODY").getJSONArray("SUBMIT_MESSAGE_RSP");
//        String result = uni_osp_body.getJSONObject(0).getString("RSP_CODE");
//
//        System.out.println();
//    }
}
