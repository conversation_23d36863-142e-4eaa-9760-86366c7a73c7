package com.example.paasconnect.app;

import com.example.paasconnect.mapper.WebServiceTaskresultMapper;
import com.example.paasconnect.util.WeComUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class TaskResultStatisticsService {
    @Resource
    private WebServiceTaskresultMapper webServiceTaskresultMapper;

    @Autowired
    private Environment env;
    
    private final static Logger log = LoggerFactory.getLogger(TaskResultStatisticsService.class);

    /**
     * 获取当天0点到当前时间的每小时统计
     */
    public List<Map<String, Object>> getTodayHourlyStats() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date start = calendar.getTime();
        Date end = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return webServiceTaskresultMapper.getHourlyStats(sdf.format(start), sdf.format(end));
    }

    public List<Map<String, Object>> getTodayTotalStats() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date start = calendar.getTime();
        Date end = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return webServiceTaskresultMapper.getTotalStats(sdf.format(start), sdf.format(end));
    }

    public String formatStatsForWeCom(List<Map<String, Object>> stats) {
        if (stats == null || stats.isEmpty()) {
            return "无探测数据";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("截至 ").append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date())).append(":\n");
        for (Map<String, Object> row : stats) {
            sb.append(row.get("name")).append("探测")
              .append(row.get("total_count")).append("次，失败")
              .append(row.get("fail_count")).append("次，成功率")
              .append(row.get("success_rate")).append("%;\n");
        }
        return sb.toString();
    }
    public void sendHourlyStatistics() {
        log.info("开始每小时业务探测统计...");
        List<Map<String, Object>> stats = getTodayTotalStats(); // 改为总计统计
        sendStatsToWeCom(stats); // 用分批发送方法，保证不割裂业务内容
        log.info("统计并发送完成");
    }

    /**
     * 发送统计内容，超900字节分批，保证业务不割裂
     */
    public void sendStatsToWeCom(List<Map<String, Object>> stats) {
        if (stats == null || stats.isEmpty()) {
            return;
        }
        String mobile = env.getProperty("alarm.yunying.mobile"); // 请补充完整配置key
        if (mobile == null || mobile.isEmpty()) {
            return;
        }
        
        // 使用新的格式化方法
        String content = formatStatsForWeCom(stats);
        
        // 检查内容长度，如果超过1600字节则分批发送
        byte[] contentBytes = content.getBytes(java.nio.charset.StandardCharsets.UTF_8);
        if (contentBytes.length <= 1600) {
            // 不超过1600字节，直接发送
            WeComUtils.sendWechar(mobile, content);
        } else {
            // 超过1600字节，按业务分批发送
            sendLargeContentInBatches(stats, mobile);
        }
    }
    
    /**
     * 大内容分批发送
     */
    private void sendLargeContentInBatches(List<Map<String, Object>> stats, String mobile) {
        List<String> messages = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append("截至 ").append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date())).append(":\n");
        int headerBytes = sb.toString().getBytes(java.nio.charset.StandardCharsets.UTF_8).length;
        int byteCount = headerBytes;
        int batchIndex = 1;
        
        for (Map<String, Object> row : stats) {
            String line = row.get("name") + "探测" + row.get("total_count") + "次，失败" + 
                         row.get("fail_count") + "次，成功率" + row.get("success_rate") + "%;\n";
            int lineBytes = line.getBytes(java.nio.charset.StandardCharsets.UTF_8).length;
            
            // 提高单批次容量到1600字节，进一步减少分批次数
            if (byteCount + lineBytes > 1600) {
                // 当前批次已满，发送并开始新批次
                messages.add(sb.toString());
                sb = new StringBuilder();
                batchIndex++;
                if (batchIndex == 2) {
                    sb.append("截至 ").append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date())).append("(续):\n");
                } else {
                    sb.append("截至 ").append(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date())).append("(").append(batchIndex).append("):\n");
                }
                int newHeaderBytes = sb.toString().getBytes(java.nio.charset.StandardCharsets.UTF_8).length;
                byteCount = newHeaderBytes;
            }
            
            sb.append(line);
            byteCount += lineBytes;
        }
        
        if (sb.length() > 0) {
            messages.add(sb.toString());
        }
        
        // 发送所有批次
        for (String msg : messages) {
            WeComUtils.sendWechar(mobile, msg);
        }
    }
} 