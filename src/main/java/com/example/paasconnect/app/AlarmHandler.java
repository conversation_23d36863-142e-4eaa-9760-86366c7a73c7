package com.example.paasconnect.app;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.example.paasconnect.entity.*;
import com.example.paasconnect.mapper.*;
import com.example.paasconnect.util.IVRUtils;
import com.example.paasconnect.util.MessageUtils;
import com.example.paasconnect.util.WeComUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.time.LocalTime;

@Component
public class AlarmHandler {
    private final static Logger log = LoggerFactory.getLogger(AlarmHandler.class);

    // 定义报警类型常量
    private static final String WEIXIN = "1";
    private static final String DUANXIN = "2";
    private static final String IVR = "3";

    @Resource
    private TbLogSendAlarmMapper tbLogSendAlarmMapper;

    @Resource
    private TbLogSendAlarmNowMapper tbLogSendAlarmNowMapper;

    @Resource
    private TbLogSendAlarmHistoryMapper tbLogSendAlarmHistoryMapper;

    @Resource
    private WebServiceMonitoringconfigurationMapper webserviceMonitoringconfigurationMapper;

    @Resource
    private WebServiceTaskresultMapper webServiceTaskresultMapper;

    @Resource
    private WebServiceDetectionunitidMapper webserviceDetectionunitidMapper;

    @Resource
    private TbLogAlarmConvergeMapper tbLogAlarmConvergeMapper;

    @Resource
    private TbLogAlarmDenyMapper tbLogAlarmDenyMapper;

    @Resource
    private TbLogSendMessageMapper tbLogSendMessageMapper;

    @Resource
    private TbLogPnmsPerformanceMapper tbLogPnmsPerformanceMapper;

    @Autowired
    private Environment env;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * @Description: 告警心跳检测
     * @param:
     * @return: void
     * @Author: guojian
     * @Date 2024/6/20 11:07
     */
    public void AlarmHealth() {
        try {
            Map<String, Object> alarmMap = new HashMap<>();
            List<TbLogSendAlarmNow> alarms = tbLogSendAlarmNowMapper.selectByMap(alarmMap);
            alarms.stream().forEach(t -> processAlarm(t));
        } catch (Exception e) {
            log.error("health error：", e);
        }
    }

    private void processAlarm(TbLogSendAlarmNow alarm) {
        String message = alarm.getEventTitle();
        List<WebServiceMonitoringconfiguration> configurations = webserviceMonitoringconfigurationMapper.queryPhone(alarm.getUnitId());

        StringBuilder weixinPhone = new StringBuilder();
        List<String> duanxinPhone = new ArrayList<>();

        if (!configurations.isEmpty()) {
            configurations.forEach(w -> {
                String alarmType = w.getAlarmType().trim();
                String phone = w.getAlarmPhone().trim();
                String remark = w.getRemark();

                if (alarmType.contains(WEIXIN)) {
                    weixinPhone.append(phone).append("|");
                }
                if (alarmType.contains(DUANXIN)) {
                    //告警规则匹配
                    log.info("remark：" + remark);
                    if (!"".equals(remark) && remark != null) {
                        boolean b = theAlarmRuleMatches(message, remark);
                        //正则匹配短信内容
                        if (b) {
                            duanxinPhone.add(phone);
                        }
                    } else {
                        duanxinPhone.add(phone);
                    }
                }
                boolean result = isTimeInNight();
                //新加0-6点不发送ivr，后续可做成配置化的
                if (message.contains("清除告警") || result) {
                    //恢复信息不发送IVR
                } else {
                    if (alarmType.contains(IVR)) {
                        IVRUtils.sendIvrAlarm(phone, message);
                    }
                }
            });
            if (duanxinPhone.size() > 0) {
                sendDuanxin(duanxinPhone, message);
            }
            if (weixinPhone.length() > 0) {
                boolean post1 = sendWeixin(weixinPhone.substring(0, weixinPhone.length() - 1), message);
                if (!post1) {
                    log.error("发送异常；", new Exception("发送失败"));
                    saveAlarmHistoryWithFailure(alarm, "微信发送状态异常");
                }
            }
        } else {
            log.error("该告警未存在订阅人；默认发送管理员");
            saveAlarmHistoryWithFailure(alarm, "未存在订阅人");
        }
        saveAlarmHistory(alarm);
        tbLogSendAlarmNowMapper.deleteById(alarm.getId());

        //告警加入 kafka 队列
        Map<String, Object> tlp = new HashMap<String, Object>();
        tlp.put("unit_id", alarm.getUnitId());
        List<TbLogPnmsPerformance> tbLogPnmsPerformances = tbLogPnmsPerformanceMapper.selectByMap(tlp);
        if(tbLogPnmsPerformances.size()>0){
            for (TbLogPnmsPerformance t : tbLogPnmsPerformances) {
                if (t.getPnmsUnitId().contains("alarm")){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date = null;
                    try {
                        date = sdf.parse(alarm.getDataTime());
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    if (t.getPnmsUnitId().contains("alarm")){
                        t.setKafkaType("1");
                        t.setKpiValue(message);
                        t.setCllTime(date);
                        t.setCllTimeStr(alarm.getDataTime());
                        tbLogPnmsPerformanceMapper.updateById(t);
                    }
                }
            }
        }

    }

    public boolean isTimeInNight() {
        LocalTime now = LocalTime.now(); // 获取当前时间
//        LocalTime now = LocalTime.of(0, 30);
        LocalTime start = LocalTime.of(0, 0); // 0点
        LocalTime end = LocalTime.of(6, 0); // 6点

        // 判断当前时间是否在0点（inclusive）到6点（exclusive）之间
        return !now.isBefore(start) && now.isBefore(end);
    }

    private boolean sendDuanxin(List<String> phones, String message) {
        log.info("短信发送开始调用");
        return phones.stream().allMatch(phone -> getMessageforduanxin(phone, message));
    }

    private boolean sendWeixin(String phones, String message) {
        log.info("企微发送开始调用");
        return getMessageforweixin(phones, message);
    }

    private void saveAlarmHistory(TbLogSendAlarmNow t) {
        TbLogSendAlarmHistory history = new TbLogSendAlarmHistory();
        history.setId(t.getId() + System.currentTimeMillis());
        history.setEventId(t.getEventId());
        history.setUnitId(t.getUnitId());
        history.setKpiId(t.getKpiId());
        history.setDataTime(t.getDataTime());
        history.setRegionCode(t.getRegionCode());
        history.setEventTitle(t.getEventTitle());
        history.setKpiValue(t.getKpiValue());
        history.setKbpClass(t.getKbpClass());
        history.setGenerantTime(t.getGenerantTime());
        history.setConfirmTime(t.getConfirmTime());
        history.setActionState(t.getActionState());
        history.setSendTime(getCurrentDate());
        history.setSendFlag("0");
        tbLogSendAlarmHistoryMapper.insert(history);
    }

    private String getCurrentDate() {
        // 使用线程安全的DateTimeFormatter
        return LocalDate.now().format(DATE_FORMATTER);
    }

    private void saveAlarmHistoryWithFailure(TbLogSendAlarmNow alarm, String err) {
        getMessageforduanxin("15510201092", "【" + err + "】" + alarm);
    }

    public boolean getMessageforduanxin(String phoneNumber, String txt) {
        String output = "";
        try {
            Thread.sleep(500);
            output = MessageUtils.sendMessages(phoneNumber, txt);
            log.info("接口出参：" + output);
            JSONObject jsonObject = JSONObject.parseObject(output);
            JSONObject uniOspBody = jsonObject.getJSONObject("UNI_BSS_BODY");
            JSONObject SM_SUBMIT_RSP = uniOspBody.getJSONObject("SM_SUBMIT_RSP");
            JSONArray submitMessageRsp = SM_SUBMIT_RSP.getJSONArray("SUBMIT_MESSAGE_RSP");
            String rspCode = submitMessageRsp.getJSONObject(0).getString("RSP_CODE");
            String rspDesc = submitMessageRsp.getJSONObject(0).getString("RSP_DESC");
            String TIMESTAMP = jsonObject.getJSONObject("UNI_BSS_HEAD").getString("TIMESTAMP");

            if (rspCode.contains("0000") || rspDesc.contains("调用成功")) {
                saveMessage(phoneNumber, txt, TIMESTAMP, output);
            } else {
                saveMessage("15510201092", "【短信发送失败】：" + txt, TIMESTAMP, output);
            }

//            String duanxinUrl = env.getProperty("alarm.duanxinUrl");
//            JSONObject object = new JSONObject();
//            object.put("ORIGIN_DOMAIN", "70");
//            JSONObject messageInfo = new JSONObject();
//            messageInfo.put("RECV_OBJECT", phoneNumber);
//            messageInfo.put("MESSAGE_CONTENT", txt);
//            object.put("MESSAGE_INFO", messageInfo);
//            JSONObject obj = new JSONObject();
//            obj.put("SUBMIT_MESSAGE_REQ", object);
//            String post = HttpClientUtil.post(duanxinUrl, obj);
//            log.info("接口入参：" + object.toJSONString());
//            log.info("接口出参：" + post);
//            JSONObject jsonObject = JSONObject.parseObject(post);
//            JSONArray uni_osp_body = jsonObject.getJSONObject("UNI_OSP_BODY").getJSONArray("SUBMIT_MESSAGE_RSP");
//            String result = uni_osp_body.getJSONObject(0).getString("RSP_CODE");
//            if (result.contains("0")) {
//                return true;
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    private boolean getMessageforweixin(String phoneNumber, String txt) {
            // String weixinUrl = env.getProperty("alarm.weixinUrl");
            // JSONObject object = new JSONObject();
            // object.put("phoneNumber", phoneNumber);
            // object.put("result", txt);
            // String post = HttpClientUtil.post(weixinUrl, object);
            // log.info("接口入参：" + object.toJSONString());
            // log.info("接口出参：" + post);
            // JSONObject jsonObject = JSONObject.parseObject(post);
            // String result = jsonObject.getJSONObject("UNI_OSP_BODY").getString("retCode");
            // if (result.contains("0")) {
            //     return true;
            // }
        return WeComUtils.sendWechar(phoneNumber, txt);
    }

    private void saveMessage(String phoneNumber, String txt, String sendTime, String outputParam) {
        TbLogSendMessage messages = new TbLogSendMessage();
        messages.setId(UUID.randomUUID().toString());
        messages.setPhone(phoneNumber);
        messages.setMessageData(txt);
        messages.setOutputParameters(outputParam);
        messages.setSendTime(sendTime);
        tbLogSendMessageMapper.insert(messages);
    }

    //告警规则匹配
    private boolean theAlarmRuleMatches(String message, String alarmRule) {
        boolean result = false;
        if (alarmRule != null && !alarmRule.isEmpty()) {
            // 使用 Pattern 和 Matcher 进行正则表达式匹配
            Pattern pattern = Pattern.compile(alarmRule);
            Matcher matcher = pattern.matcher(message);
            if (matcher.find()) {
                result = true;
            }
        }
        return result;
    }

//    public static void main(String[] args) {
//
//        System.out.println(new AlarmHandler().theAlarmRuleMatches("【业务监控-手机探测】[一般告警]任务名：北京联通-经理工作台,探测结果:失败，失败原因：切换至专项监控页面时间超过设定阈值，探测结束,探测时间：2024-11-14 22:19:16,耗时：117699毫秒","首页|晨训页面|晚结页面|预警页面|业绩看板"));
//    }
}