package com.example.paasconnect.app;

import com.example.paasconnect.entity.*;
import com.example.paasconnect.mapper.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
public class PhoneService {

    private final static Logger log = LoggerFactory.getLogger(PhoneService.class);

    @Resource
    private TbLogSendAlarmNowMapper tbLogSendAlarmNowMapper;

    @Resource
    private WebServiceMonitoringconfigurationMapper webserviceMonitoringconfigurationMapper;

    @Resource
    private WebServiceTaskresultMapper webServiceTaskresultMapper;

    @Resource
    private WebServiceDetectionunitidMapper webserviceDetectionunitidMapper;

    @Resource
    private TbLogAlarmConvergeMapper tbLogAlarmConvergeMapper;

    @Resource
    private TbLogAlarmDenyMapper tbLogAlarmDenyMapper;

    /**
     * @Description: 定时查询探测数据，如1小时内无探测数据，则告警
     * @param:
     * @return: void
     * @Author: guojian
     * @Date 2024/10/12 13:34
     */
    public void phoneTaskEnable() {
        log.info("开始校验前小时的探测数据...");
        // 优化睡眠中断处理
        try {
            getDevicesList();
            Thread.sleep(1000);
            getPcEvent();
        } catch (Exception e) {
            log.error("线程被中断", e);
        }
    }
    private String getDateDay() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(new Date());
    }
    private String getDateDayTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date());
    }
    private List<WebServiceTaskresult> getTaskResultByDeviceId(String deviceId) {
        List<WebServiceTaskresult> successData = new ArrayList<>();
        String time = getDateTime(60);
        log.info("..." + time);
        successData = webServiceTaskresultMapper.getTaskDataBydeviceId(deviceId, time, null);
        return successData;
    }
    
    private List<WebServiceTaskresult> getTaskResultByUnitId(String unitId) {
        List<WebServiceTaskresult> successData = new ArrayList<>();
        String time = getDateTime(60);
        log.info("..." + time);
        successData = webServiceTaskresultMapper.getTaskData(unitId, time,null);
        return successData;
    }
    private String getDateTime(int beginTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (beginTime == 0) {
            return sdf.format(new Date());
        } else {
            LocalDateTime currentTime = LocalDateTime.now();
            LocalDateTime time30MinutesAgo = currentTime.minusMinutes(beginTime);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            log.info("Current Time: " + currentTime.format(formatter));
            log.info("Time 30 minutes ago: " + time30MinutesAgo.format(formatter));
            return time30MinutesAgo.format(formatter);
        }
    }
    private void getDevicesList(){
        List<WebServiceDetectionunitid> devicesList = webserviceDetectionunitidMapper.getDevicesList();
        if(devicesList.size() > 0){
            for(WebServiceDetectionunitid device : devicesList){
                List<WebServiceTaskresult> taskResult = getTaskResultByDeviceId(device.getUuitId());
                if (taskResult.size() > 0) {
                    log.info("本次设备：" + device.getUuitId()+", " + device.getTaskFile(),"探测次数统计：" + taskResult.size() +"，无需告警");
                } else {
                    log.info("前小时的探测数据不存在，开始告警...");
                    //获取未发送告警的告警信息
                    String text = "当前探测手机【" + device.getTaskFile() + "】未进行探测，请查看手机状态是否正常,手机卡死，请关闭卡死软件，防止影响正常业务";
                    TbLogSendAlarmNow now = new TbLogSendAlarmNow();
                    now.setId(create());
                    now.setEventId("phoneTaskEnable");
                    now.setUnitId("phoneTaskEnable");
                    now.setKpiId(null);
                    now.setDataTime(getDateTime(0));
                    now.setRegionCode(null);
                    now.setEventTitle(text);
                    now.setSendFlag("1");
                    now.setSendTime(getDateDay());
                    tbLogSendAlarmNowMapper.insert(now);
                }
            }
        }
    }

    /**
     * @Description: 检查 pc 服务器正在执行的任务
     * @param:
     * @return: void
     * @Author: guojian
     * @Date 2025/6/17 16:42
     */
    private void getPcEvent(){
        List<WebServiceDetectionunitid> devicesList = webserviceDetectionunitidMapper.getPcUnitId();
        if(devicesList.size() > 0){
            for(WebServiceDetectionunitid device : devicesList){
                List<WebServiceTaskresult> taskResult = getTaskResultByUnitId(device.getTaskFile());
                if (taskResult.size() > 0) {
                    log.info("本次PC任务：" + device.getUuitId()+", " + device.getTaskFile(),"探测次数统计：" + taskResult.size() +"，无需告警");
                } else {
                    log.info("前小时的探测数据不存在，开始告警...");
                    //获取未发送告警的告警信息
                    String text = device.getTaskFile() +"【"+device.getUuitId()+"】近 1 小时未进行探测，请及时查看，时间："+getDateDayTime();
                    TbLogSendAlarmNow now = new TbLogSendAlarmNow();
                    now.setId(create());
                    now.setEventId("pcTaskUnitIdEnable");
                    now.setUnitId("pcTaskUnitIdEnable");
                    now.setKpiId(null);
                    now.setDataTime(getDateTime(0));
                    now.setRegionCode(null);
                    now.setEventTitle(text);
                    now.setSendFlag("1");
                    now.setSendTime(getDateDay());
                    tbLogSendAlarmNowMapper.insert(now);
                }
            }
        }
    }

    public String create() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replaceAll("-", "");
    }

}
