package com.example.paasconnect.app;/**
 * <AUTHOR>
 * @function com.example.paasconnect.app
 * @date 2022/3/12 15:55
 */

import com.alibaba.druid.filter.config.ConfigTools;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.example.paasconnect.ai.IPaasConnet;
import com.example.paasconnect.ai.ISaveData;
import com.example.paasconnect.entity.*;
import com.example.paasconnect.mapper.*;
import com.example.paasconnect.util.HttpClientUtil;
import com.example.paasconnect.util.ProxysqlClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName PaasConnetImpl
 * @Deacription TODO
 * <AUTHOR>
 * @Date 2022/3/12 15:55
 * @Version 1.0
 **/

@Service
public class PaasConnetImpl implements IPaasConnet {
    private final static Logger log = LoggerFactory.getLogger(PaasConnetImpl.class);

    // 注入对象
    @Autowired
    private Environment env;

    @Value("${paas.mysql.user}")
    private String user;

    @Value("${paas.mysql.pwd}")
    private String pwd;

    @Resource
    private PaasVipBusinessMapper paasVipBusinessMapper;

    @Resource
    private PaasVipAssociateIpMapper paasVipAssociateIpMapper;

    @Resource
    private PaasVipConfigMapper paasVipConfigMapper;

    @Resource
    private ISaveData saveData;

    @Resource
    UnicomVerbNounMapper unicomVerbNounMapper;

    @Resource
    TbLogSendAlarmMapper tbLogSendAlarmMapper;

    @Resource
    TbLogSendAlarmNowMapper tbLogSendAlarmNowMapper;

    @Resource
    WebserviceBackupmessageMapper webserviceBackupmessageMapper;

    @Override
    public String coll(String data) {
        JSONObject all = new JSONObject();
        try {
            //默认数据解密
            pwd = ConfigTools.decrypt(pwd);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            int i = -1;
            JSONObject parse = JSONObject.parseObject(data);
            String vip = parse.getString("vip");
            String managePort = parse.getString("managePort");
            List<PaasVipBusiness> schmeList = JSON.parseArray(parse.getString("schmeList"), PaasVipBusiness.class);

            String ips = parse.getString("ips");
            String[] split = ips.split(",");

            for (int j = 0; j < split.length; j++) {
                String ip = split[j];
                Integer port = Integer.parseInt(managePort);

                if (parse.containsKey("user")) {
                    user = parse.getString("user");
                }
                if (parse.containsKey("pwd")) {
                    pwd = parse.getString("pwd");
                }
                String sql = "show @@connection";
                if (parse.containsKey("sql")) {
                    sql = parse.getString("sql");
                }
                if (all.isEmpty()) {
                    all = ProxysqlClient.executeQuerySize(ip, port, schmeList.get(0).getDatabaseName(), user, "Unionmon123", sql, 10, schmeList);
                    log.info(ip + ":" + port + "_" + "执行结果:" + all.toJSONString());
                } else {
                    JSONObject jsonObject = ProxysqlClient.executeQuerySize(ip, port, schmeList.get(0).getDatabaseName(), user, pwd, sql, 10, schmeList);
                    log.info(ip + ":" + port + "_" + "执行结果:" + jsonObject.toJSONString());
                    // 数据合并
                    Set<String> strings = jsonObject.keySet();
                    for (String s : strings) {
                        if (all.containsKey(s)) {
                            int intValue = jsonObject.getIntValue(s);
                            int allIntValue = all.getIntValue(s);
                            int total = intValue + allIntValue;
                            all.put(s, total);
                        } else {
                            all.put(s, jsonObject.getIntValue(s));
                        }
                    }
                }
            }
            all.put("vip", vip);
        } catch (Exception e) {
            log.error("采集异常：", e);
        }
        return all.toJSONString();
    }

    public String create() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replaceAll("-", "");
    }

    @Override
    public String getIpList(String vips) {
        log.info("开始调用");
        String re = "";
        try {
            //采集时间记录
            long l = System.currentTimeMillis();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat day = new SimpleDateFormat("yyyy-MM-dd");
            String Time = formatter.format(l);
            String formatDay = day.format(l);

            Map<String, Object> a = new HashMap<String, Object>();
            if (!vips.isEmpty()) {
                a.put("vip", vips);
            }

            List<PaasVipConfig> paasVipConfigs = paasVipConfigMapper.selectByMap(a);
            log.info("获取数据如下" + paasVipConfigs.toString());
            if (paasVipConfigs.size() > 0) {
                for (int i = 0; i < paasVipConfigs.size(); i++) {
                    //部署ip 和 端口
                    String deploymentIp = paasVipConfigs.get(i).getDeploymentIp();
                    String vip = paasVipConfigs.get(i).getVip();
                    String ips = paasVipConfigs.get(i).getIps();

                    Map<String, Object> mapVip = new HashMap<>();
                    mapVip.put("vip", vip);
                    List<PaasVipAssociateIp> paasVipAssociateIps = paasVipAssociateIpMapper.selectByMap(mapVip);

                    if (paasVipAssociateIps.size() > 0) {
                        List<PaasTbConnectionData> list = new ArrayList<>();
                        for (int j = 0; j < paasVipAssociateIps.size(); j++) {
                            String managePort = paasVipAssociateIps.get(j).getManagePort();
                            mapVip.put("manage_port", managePort);
                            List<PaasVipBusiness> businessList = paasVipBusinessMapper.selectByMap(mapVip);
                            //数据拼装
                            JSONObject obj = new JSONObject();
                            obj.put("vip", vip);
                            obj.put("managePort", managePort);
                            obj.put("ips", ips);
                            obj.put("schmeList", businessList);
                            log.info("采集接口入参：" + obj.toJSONString());
                            String url = "http://" + deploymentIp + "/executeQuery";
                            String post = HttpClientUtil.post(url, obj);
                            if (!post.isEmpty() && !"{}".equals(post)) {
                                JSONObject jsonObject = JSONObject.parseObject(post);
                                for (PaasVipBusiness b : businessList) {
                                    if (jsonObject.containsKey(b.getDatabaseName())) {
                                        PaasTbConnectionData p = new PaasTbConnectionData();
                                        p.setId(create());
                                        p.setDate(formatDay);
                                        p.setBusinessName(b.getBusinessName());
                                        p.setDatabaseName(b.getDatabaseName());
                                        p.setManagePort(managePort);
                                        p.setServicePort(b.getServicePort());
                                        p.setConnet(jsonObject.getString(b.getDatabaseName()));
                                        p.setVip(vip);
                                        p.setTime(Time);
                                        list.add(p);
                                    }
                                }
                            }
                        }
                        saveData.saveBatch(list);
                    }
                }
            }
            re = "success";
        } catch (Exception e) {
            re = "fail";
            log.error("getIpList接口异常：", e);
        }
        return re;
    }

    @Override
    public String verbNoun(String data) {
        Map<String, Object> RESULT = new HashMap<String,Object>();
        try {
            boolean notEmpty = StringUtils.isNotEmpty(data);
            if (notEmpty) {
                JSONObject json = JSONObject.parseObject(data);
                JSONArray objects = json.getJSONArray("data");
                List<UnicomVerbNoun> unicomVerbNouns = unicomVerbNounMapper.selectByMap(new HashMap<>());
                if (unicomVerbNouns.size() > 0) {
                    for (int i = 0; i < objects.size(); i++) {
                        JSONObject jsonObject = objects.getJSONObject(i);
                        String title = jsonObject.getString("title");
                        String value = jsonObject.getString("value");
                        boolean isof = true;
                        for (UnicomVerbNoun vn:unicomVerbNouns) {
                            if(title.equals(vn.getTitle())){
                                isof = false;
                                continue;
                            }
                        }
                        if (isof) {
                            TbLogSendAlarmNow alarm = new TbLogSendAlarmNow();
                            alarm.setId(create());
                            alarm.setEventId("同事吧"+ new Date().getTime());
                            alarm.setUnitId("tongshiba0117");
                            alarm.setDataTime(new Date().getTime() +"");
                            alarm.setEventTitle("同事吧新增提醒：\n"+"标题:"+title+"\n"+"内容:"+value);
                            tbLogSendAlarmNowMapper.insert(alarm);
                        }
                    }
                }
                //更新数据
                boolean b = updateData(objects,unicomVerbNouns);
                RESULT.put("code","0");
                RESULT.put("msg","success");
            } else {
                RESULT.put("code","500");
                RESULT.put("msg","请求数据异常");
                log.error("数据异常：", data);
            }
        } catch (Exception e) {
            RESULT.put("code","500");
            RESULT.put("msg","异常"+e.getMessage());
            log.error("verbNoun：", e);
        }
        return RESULT.toString();
    }
    private  boolean updateData(JSONArray data,List<UnicomVerbNoun> li){
        if(li.size() > 0){
            List<String> st = new ArrayList<>();
            for (UnicomVerbNoun u:li) {
                st.add(u.getId());
            }
            int delete = unicomVerbNounMapper.deleteBatchIds(st);
            if(delete <= 0){
                return false;
            }
        }
        UnicomVerbNoun unicom = new UnicomVerbNoun();
        for (int i = 0; i < data.size(); i++) {
            JSONObject jsonObject = data.getJSONObject(i);
            String title = jsonObject.getString("title");
            String value = jsonObject.getString("value");
            unicom.setId(create());
            unicom.setTitle(title);
            unicom.setValue(value);
            unicomVerbNounMapper.insert(unicom);
        }
        return true;
    }

    @Override
    public String getSmsCode(String data) {
        Map<String, Object> RESULT = new HashMap<String,Object>();
        try {
            boolean notEmpty = StringUtils.isNotEmpty(data);
            if (notEmpty) {
                log.info("获取短信验证码："+data);
                JSONObject json = JSONObject.parseObject(data);
                JSONObject data1 = json.getJSONObject("data");
                WebserviceBackupmessage unicom = new WebserviceBackupmessage();
                String phone = data1.getString("phone");
                String code = data1.getString("code");
                String time = data1.getString("time");
                //"time":1744021910880
                unicom.setId(create());
                unicom.setPhone(phone);
                unicom.setCode(code);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedTime = sdf.format(new Date(Long.parseLong(time)));
                unicom.setTime(formattedTime);
                webserviceBackupmessageMapper.insert(unicom);
                RESULT.put("code","0");
                RESULT.put("msg","success");
            } else {
                RESULT.put("code","500");
                RESULT.put("msg","请求数据异常");
                log.error("数据异常：", data);
            }
        } catch (Exception e) {
            RESULT.put("code","500");
            RESULT.put("msg","异常"+e.getMessage());
            log.error("getSmsCode：", e);
        }
        return RESULT.toString();
    }


}
