/**   
 * @Title: RabbitMQException.java
 * @Package com.sitech.ssd.exception
 * @Description: TODO(用一句话描述该文件做什么)
 * <AUTHOR>  
 * @version 1.0
 * @createtime 2013-7-7 下午2:12:29
 */
package com.example.paasconnect.exception;

/**
 * <p>
 * Title: HttpClientException
 * </p>
 * <p>
 * Description: HttpClient异常
 * </p>
 * <p>
 * Copyright: Copyright (c) 2013
 * </p>
 * <p>
 * Company: SI-TECH
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @createtime 2013-7-7 下午2:12:29
 * 
 */
public class HttpClientException extends Exception {
	/**
	 * @Fields serialVersionUID : TODO(用一句话描述这个变量表示什么)
	 */
	private static final long serialVersionUID = -1924259117159413797L;

	public HttpClientException() {
		super();
	}

	public HttpClientException(String message) {
		super(message);
	}

	public HttpClientException(String message, Throwable cause) {
		super(message, cause);
	}

	public HttpClientException(Throwable cause) {
		super(cause);
	}
}
