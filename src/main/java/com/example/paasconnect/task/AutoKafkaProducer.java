package com.example.paasconnect.task;

import com.alibaba.fastjson.JSONObject;
import com.example.paasconnect.entity.PortConnectivityAuditResults;
import com.example.paasconnect.entity.TbLogPnmsPerformance;
import com.example.paasconnect.mapper.PortConnectivityAuditResultsMapper;
import com.example.paasconnect.mapper.TbLogPnmsPerformanceMapper;
import com.example.paasconnect.mapper.WebServiceTaskresultMapper;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class AutoKafkaProducer {

    private final static Logger log = LoggerFactory.getLogger(AutoKafkaProducer.class);

    @Resource
    private TbLogPnmsPerformanceMapper tbLogPnmsPerformanceMapper;

    @Resource
    private WebServiceTaskresultMapper webServiceTaskresultMapper;

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ExecutorService executor = Executors.newSingleThreadExecutor();

    @Resource
    private PortConnectivityAuditResultsMapper portConnectivityAuditResultsMapper;


    public AutoKafkaProducer(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    @PostConstruct
    public void startProducing() {
        Thread producerThread = new Thread(() -> {
            try {
                while (true) {
                    Thread.sleep(10 * 1000);
                    Map<String, Object> tlp = new HashMap<String, Object>();
                    tlp.put("kafka_type", "1");
                    List<TbLogPnmsPerformance> tbLogPnmsPerformances = tbLogPnmsPerformanceMapper.selectByMap(tlp);
                    if (tbLogPnmsPerformances.size() > 0) {
                        for (TbLogPnmsPerformance tbLogPnmsPerformance : tbLogPnmsPerformances) {
                            String message = JSONObject.toJSONString(tbLogPnmsPerformance);
                            kafkaTemplate.send("metrics", message);
                            log.info("Sent metrics message: " + message);
                            tbLogPnmsPerformance.setKafkaType("0");
                            int i = tbLogPnmsPerformanceMapper.updateById(tbLogPnmsPerformance);
                            log.info(tbLogPnmsPerformance.getId() + "更新状态: " + i);
                        }
                    } else {
                        log.info("本次推送暂无数据数据");
                    }
                    Thread.sleep(20 * 1000);
                    getDetail();
                    Thread.sleep(20 * 1000);
                    getInternetPort();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Producer thread interrupted: " + e.getMessage());
            }
        });
        producerThread.setName("Kafka-Producer-Thread");
        producerThread.setDaemon(true);  // 设置为守护线程
        producerThread.start();
    }
    /**
     * @Description: 获取互联网端口探测明细
     * @param:
     * @return: java.lang.String
     * @Author: guojian
     * @Date 2025/5/22 16:16
     */
    private void getInternetPort() {
        LocalDateTime now = LocalDateTime.now();
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        // 格式化时间
        String current_time = now.format(formatter);
        log.info("开始获取互联网端口探测明细: " + current_time);
        List<PortConnectivityAuditResults> portConnectivityAuditResults = portConnectivityAuditResultsMapper.seleteByTime(current_time);
        log.info("本次推送 kafka 数据条数: " + portConnectivityAuditResults.size());
        if  (portConnectivityAuditResults.size() > 0) {
            for (PortConnectivityAuditResults portConnectivityAuditResult : portConnectivityAuditResults) {
                //获取 kbp_class
                List<PortConnectivityAuditResults> maps = portConnectivityAuditResultsMapper.gettbLogPnmsPerformanceByID(portConnectivityAuditResult.getName());
                if (maps.size() == 0) {
                    continue;
                }
                String createTime = portConnectivityAuditResult.getCreateTime();
                log.info("createTime: " + createTime);

                DateTimeFormatter formatterWithMillis = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                DateTimeFormatter formatterWithoutMillis = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                LocalDateTime cllTime = null;

                try {
                    cllTime = LocalDateTime.parse(createTime, formatterWithMillis);
                } catch (DateTimeParseException e) {
                    try {
                        cllTime = LocalDateTime.parse(createTime, formatterWithoutMillis);
                    } catch (DateTimeParseException ex) {
                        ex.printStackTrace(); // 处理无法解析的情况
                        continue;
                    }
                }

                log.info("cllTime: " + cllTime);
                log.info("cllTimeStr: " + createTime);
                List<JSONObject> messageList = new ArrayList<>();
                // 固定的 KPI IDs
                String[] kpiIds = {"FM-25-02-001-08", "FM-25-02-001-09", "FM-25-02-001-10", "FM-25-02-001-11"};
//                {"cllTime":1748401751000,"cllTimeStr":"2025-05-28 11:09:11", "extUnitId":"11-123-02","interva":"900000","kpiDetail":"最新探测状态","kpiId":"PM-25-02-001-09",
//                "kpiName":"最新探测状态","kpiValue":"成功","unitId":"11-123-02:woyishou-001","unitName":"沃易售APP"}
                // 遍历每个 KPI ID
                String ipAddress = portConnectivityAuditResult.getIpAddress();
                String[] parts = ipAddress.split(":");
                String ipWithoutPort = parts[0];
                String deviceId = ipWithoutPort.replace('.', '_');
                String port = parts[1];

                for (String kpiId : kpiIds) {
                    JSONObject object = new JSONObject();
                    // 组装数据
                    object.put("cllTimeStr",createTime);
                    object.put("cllTime", cllTime);
                    object.put("extUnitId", maps.get(0).getAuditId());
                    log.info("aicode: " + maps.get(0).getAuditId());
                    object.put("interval", 900000);
//                    String linkUnitId = kbpClass + ":" + deviceId + "-" + agentIp + "_" + host + "^" + port;
//                    11-142-01:123_12_72_188-***********_123.112.72.188^端口
                    object.put("unitId", maps.get(0).getAuditId() + ":" + deviceId+"-***********_" + ipWithoutPort + "^" + port);
                    object.put("kpiId", kpiId);
                    object.put("unitName", portConnectivityAuditResult.getName());
                    if("FM-25-02-001-08".equals(kpiId)){
                        object.put("kpiValue", portConnectivityAuditResult.getFinalStatus());
                        object.put("kpiDetail", "手机公网业务端口探测状态");
                        object.put("kpiName", "手机公网业务端口探测状态");
                    }else if("FM-25-02-001-09".equals(kpiId)){
                        object.put("kpiValue", "***********-"+portConnectivityAuditResult.getIpAddress());
                        object.put("kpiDetail", "手机公网链路IP");
                        object.put("kpiName", "手机公网链路IP");
                    }else if("FM-25-02-001-10".equals(kpiId)){
                        object.put("kpiValue", portConnectivityAuditResult.getIpAddress());
                        object.put("kpiDetail", "手机公网业务探测地址");
                        object.put("kpiName", "手机公网业务探测地址");
                    }else if("FM-25-02-001-11".equals(kpiId)){
                        object.put("kpiValue", portConnectivityAuditResult.getCallTime());
                        object.put("kpiDetail", "手机公网业务响应时间");
                        object.put("kpiName", "手机公网业务响应时间");
                    }
                    // 将组装好的数据添加到列表中
                    messageList.add(object);
                }
                // 将所有组装好的数据一次性推送到 Kafka
                for (JSONObject message : messageList) {
                    String jsonMessage = JSONObject.toJSONString(message);
                    kafkaTemplate.send("metrics", jsonMessage);
                    log.info("Sent metrics message: " + jsonMessage);
                }
            }
          }
    }
    //port_connectivity_audit_results

    //更新探测数据
    private void getDetail() {
        List<Map<String, Object>> maps = webServiceTaskresultMapper.gettbLogPnmsPerformanceByID();
        if (maps.size() > 0) {
            for (Map<String, Object> map : maps) {
                if (map == null || map.get("cll_time_str") == null) {
                    continue;
                }
                String cllTimeStr = map.get("cll_time_str").toString();
                Map<String, Object> taskDataByTaskId = webServiceTaskresultMapper.getTaskDataByTaskId(map.get("unit_id").toString());

                if (taskDataByTaskId == null || taskDataByTaskId.get("latestDetectionTime") == null) {
                    log.warn("taskDataByTaskId is null or latestDetectionTime is null for unit_id: " + map.get("unit_id"));
                    continue;
                }
                String latestDetectionTime1 = taskDataByTaskId.get("latestDetectionTime").toString();
                log.info("最新采集时间初始化数据: " + latestDetectionTime1);

                //按固定格式比对
                Date latestDetectionTime = formatDate(latestDetectionTime1);

                // 转换为目标格式
                SimpleDateFormat targetFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String latestDetectionTimeStr = targetFormat.format(latestDetectionTime);

                if (!"".equals(cllTimeStr) && !"".equals(latestDetectionTimeStr)) {
                    log.info("kafka采集cllTime: " + cllTimeStr);
                    log.info("最新采集时间: " + latestDetectionTimeStr);
                    int comparisonResult = 0;
                    if (latestDetectionTimeStr.length() >= 16 && latestDetectionTimeStr.length() >= 16) {
                        String cllTimeMinute = cllTimeStr.substring(0, 16);
                        String latestDetectionTimeMinute = latestDetectionTimeStr.substring(0, 16);
                        log.info("比较时间 - cllTimeMinute: " + cllTimeMinute + ", latestDetectionTimeMinute: " + latestDetectionTimeMinute);
                        comparisonResult = cllTimeMinute.compareTo(latestDetectionTimeMinute);
                    }

                    if (comparisonResult < 0) {
                        log.info("kafka采集cllTime 在 最新采集时间 之前");
                        Map<String, Object> tlp = new HashMap<String, Object>();
                        tlp.put("unit_id", map.get("unit_id").toString());
                        List<TbLogPnmsPerformance> tbLogPnmsPerformances = tbLogPnmsPerformanceMapper.selectByMap(tlp);
                        for (TbLogPnmsPerformance tbLogPnmsPerformance : tbLogPnmsPerformances) {
                            if ("CM-25-02-001-01".equals(tbLogPnmsPerformance.getKpiId())) {
                                //公网探测地址默认不需要调整
                            } else if (tbLogPnmsPerformance.getPnmsUnitId().contains("alarm")) {
                                continue;
                            } else if ("PM-25-02-001-09".equals(tbLogPnmsPerformance.getKpiId())) {
                                String latestDetectionStatus = taskDataByTaskId.get("latestDetectionStatus").toString();
                                tbLogPnmsPerformance.setKpiValue("0".equals(latestDetectionStatus) ? "失败" : "成功");
                            } else if ("PM-25-02-001-10".equals(tbLogPnmsPerformance.getKpiId())) {
                                tbLogPnmsPerformance.setKpiValue(latestDetectionTimeStr);
                            } else if ("PM-25-02-001-11".equals(tbLogPnmsPerformance.getKpiId())) {
                                tbLogPnmsPerformance.setKpiValue("".equals(taskDataByTaskId.get("latestDetectionReason").toString()) ? "无" : taskDataByTaskId.get("latestDetectionReason").toString());
                            } else if ("PM-25-02-001-12".equals(tbLogPnmsPerformance.getKpiId())) {
                                tbLogPnmsPerformance.setKpiValue(taskDataByTaskId.get("totalDetections").toString());
                            } else if ("PM-25-02-001-13".equals(tbLogPnmsPerformance.getKpiId())) {
                                tbLogPnmsPerformance.setKpiValue(taskDataByTaskId.get("successRate").toString() + "%");
                            }
                            tbLogPnmsPerformance.setKafkaType("1");
                            tbLogPnmsPerformance.setCllTime(latestDetectionTime);
                            tbLogPnmsPerformance.setCllTimeStr(latestDetectionTimeStr);
                            tbLogPnmsPerformanceMapper.updateById(tbLogPnmsPerformance);
                        }
                    } else if (comparisonResult == 0) {
                        System.out.println("cllTime 等于 latestDetectionTime");
                    } else {
                        System.out.println("cllTime 在 latestDetectionTime 之后");
                    }
                }
            }
        }

    }

    private Date formatDate(String isoDateStr) {
        // 解析ISO 8601格式，支持带秒和不带秒的格式
        SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        SimpleDateFormat isoFormatWithoutSeconds = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm");
        Date date = null;
        try {
            date = isoFormat.parse(isoDateStr);
        } catch (ParseException e1) {
            try {
                date = isoFormatWithoutSeconds.parse(isoDateStr);
            } catch (ParseException e2) {
                throw new RuntimeException("无法解析日期格式：" + isoDateStr, e2);
            }
        }
        return date;
    }



//    @PostConstruct
//    public void startProducing() {
//        executor.submit(() -> {
//            try {
//                for (int i = 0; i < 100; i++) {
//                    if (Thread.currentThread().isInterrupted()) {
//                        break;  // 如果线程被中断，退出循环
//                    }
//                    String message = "Test message " + i;
//                    kafkaTemplate.send("metrics", message);
//                    System.out.println("Sent message: " + message);
//                    Thread.sleep(1000);
//                }
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//            }
//        });
//    }
//
//    @PreDestroy
//    public void shutdown() {
//        executor.shutdown();  // 停止接收新任务
//        try {
//            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
//                executor.shutdownNow();  // 强制终止正在执行的任务
//            }
//        } catch (InterruptedException e) {
//            executor.shutdownNow();
//        }
//    }

    @Configuration
    public static class KafkaProducerConfig {

        @Bean
        public ProducerFactory<String, String> producerFactory() {
            Map<String, Object> configProps = new HashMap<>();
            configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                    "132.91.175.40:9092,132.91.175.41:9092,132.91.175.42:9092");
            configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
            configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

            // SASL/PLAIN 认证配置
            configProps.put("security.protocol", "SASL_PLAINTEXT");
            configProps.put("sasl.mechanism", "PLAIN");
            configProps.put("sasl.jaas.config",
                    "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"user11\" password=\"2xFm8wAOyQ\";");

            return new DefaultKafkaProducerFactory<>(configProps);
        }

        @Bean
        public KafkaTemplate<String, String> kafkaTemplate() {
            return new KafkaTemplate<>(producerFactory());
        }
    }

//    public static void main(String[] args) {
//        String createTime = "2025-05-27 15:33:21.000";
//        // 定义时间格式
//        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
//        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//
//        String cllTimeStr = "";
//        Date cllTime = null;
//        try {
//            // 将 createTime 转换为 Date 对象
//            cllTime = inputFormat.parse(createTime);
//            // 格式化为字符串（不带毫秒）
//            cllTimeStr = outputFormat.format(cllTime);
//            System.out.println("createTime 转换为不带毫秒的时间字符串: " + cllTimeStr) ;
//            System.out.println("cllTime 转换为不带毫秒的时间字符串: " + cllTime) ;
//        } catch (ParseException e) {
//            log.error("解析 createTime 失败: " + e.getMessage());
//        }
//    }
}
