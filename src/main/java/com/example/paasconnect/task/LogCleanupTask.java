package com.example.paasconnect.task;

import com.example.paasconnect.util.LogUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 日志清理定时任务
 * 定期清理过期的日志文件
 */
@Component
public class LogCleanupTask {
    private static final Logger logger = LoggerFactory.getLogger(LogCleanupTask.class);
    private static final int LOG_RETENTION_DAYS = 30; // 日志保留天数
    
    /**
     * 应用启动时创建日志目录并设置系统属性
     */
    @PostConstruct
    public void init() {
        // 创建日志目录
        LogUtil.createLogDirectoryIfNotExists();
        
        // 设置系统属性，供 log4j2 使用
        System.setProperty("log.home", LogUtil.getLogHome());
        
        logger.info("日志管理初始化完成，日志目录: {}, 日志保留天数: {} 天", 
                LogUtil.getLogHome(), LOG_RETENTION_DAYS);
    }
    
    /**
     * 每天凌晨 2 点执行日志清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldLogs() {
        logger.info("开始执行日志清理任务...");
        LogUtil.cleanupOldLogFiles(LOG_RETENTION_DAYS);
        logger.info("日志清理任务完成");
    }
} 