package com.example.paasconnect.task;

import com.alibaba.fastjson.JSONObject;
import com.example.paasconnect.ai.IHealth;
import com.example.paasconnect.app.AlarmHandler;
import com.example.paasconnect.app.AlarmService;
import com.example.paasconnect.app.PhoneService;
import com.example.paasconnect.app.TaskResultStatisticsService;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
public class SaticScheduleTask {

    @Resource
    private IHealth health;

    @Resource
    AlarmHandler alarmHandler;

    @Resource
    AlarmService alarmService;

    @Resource
    PhoneService phoneService;


    @Resource
    private TaskResultStatisticsService taskResultStatisticsService;

    //3.短信发送
    @Scheduled(cron = "0/15 * * * * ?")
    private void configureTasks() {
        System.err.println("执行静态定时任务时间: " + LocalDateTime.now());
//        health.health();
        alarmHandler.AlarmHealth();
    }

    @Scheduled(cron = "0 0/1 * * * ?")
    private void alarmTasks() {
        System.err.println("告警收敛定时任务: " + LocalDateTime.now());
        alarmService.alarmConverge();
    }

    /**
     * @Description: 定时排查失败探测次数,如果手机没有生成告警
     * @param:
     * @return: void
     * @Author: guojian
     * @Date 2024/10/12 13:30
     */
    @Scheduled(cron = "0 0/2 * * * ?")
    private void VerifyTheAlarm() {
        System.err.println("定时排查失败探测次数开始: " + LocalDateTime.now());
        alarmService.VerifyTheAlarm();
    }

    /**
     * @Description: 每小时检查手机状态
     * @param:
     * @return: void
     * @Author: guojian
     * @Date 2024/10/12 13:30
     */
    @Scheduled(cron = "0 10 * * * ?")
    private void phoneTasks() {
        System.err.println("每小时检查手机状态: " + LocalDateTime.now());
        phoneService.phoneTaskEnable();
    }


    /**
     * 每小时整点统计并发送企业微信
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void sendHourlyStatistics() {
        taskResultStatisticsService.sendHourlyStatistics();
    }

}