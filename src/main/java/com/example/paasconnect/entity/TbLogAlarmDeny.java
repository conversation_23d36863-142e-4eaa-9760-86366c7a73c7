package com.example.paasconnect.entity;


import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @title: 实体类
 * @Description:
 * <AUTHOR>
 * @date 2022/3/23 18:31
 * @return
 */
@Table
@Data
public class TbLogAlarmDeny {

    @Id
    private String id;

    private String alarmId;

    private String unitId;

    private String unitName;

    private String alarmType;
}
