package com.example.paasconnect.entity;


import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @title:
 * @Description: 实体类
 * <AUTHOR>
 * @date 2022/3/12 15:14
 * @return
 */
@Table
@Data
public class PortConnectivityAuditResults {

    @Id
    private String auditId;
    private String name;
    private String ipAddress;
    private Integer finalStatus;
    private Integer failureCount;
    private String callTime;
    private String taskId;
    private String unitId;
    private String createTime;
    private String lastFailureTime;
}
