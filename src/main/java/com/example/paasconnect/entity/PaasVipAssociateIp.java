package com.example.paasconnect.entity;


import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @title:
 * @Description: 实体类
 * <AUTHOR>
 * @date 2022/3/12 15:14
 * @return
 */
@Table
@Data
public class PaasVipAssociateIp {

    @Id
    private String id;

    private String vip;

    private String servicePort;

    private String managePort;

    private String remark;


}
