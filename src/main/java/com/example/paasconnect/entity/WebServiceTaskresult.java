package com.example.paasconnect.entity;


import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

@Table
@Data
public class WebServiceTaskresult {

    @Id
    private String id;

    private String taskId;

    private String taskFileId;

    private String startTime;

    private String endTime;

    private String time;

    private String status;

    private String deviceId;

    private String reason;
}
