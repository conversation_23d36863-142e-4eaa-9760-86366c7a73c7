package com.example.paasconnect.entity;


import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @title: 实体类
 * @Description:
 * <AUTHOR>
 * @date 2022/3/23 18:31
 * @return
 */
@Table
@Data
public class TbLogSendAlarmNow {

    @Id
    private String id;

    private String eventId;

    private String unitId;

    private String kpiId;

    private String dataTime;

    private String regionCode;

    private String eventTitle;

    private String kpiValue;

    private String kbpClass;

    private String generantTime;

    private String confirmTime;

    private String actionState;

    private String sendTime;

    private String sendFlag;


}
