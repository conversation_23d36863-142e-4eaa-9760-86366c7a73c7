package com.example.paasconnect.entity;


import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @title: 实体类
 * @Description:
 * <AUTHOR>
 * @date 2022/3/23 18:31
 * @return
 */
@Table
@Data
public class TbLogAlarmConfiguration {

    @Id
    private String unitId;

    private String unitName;

    private String deviceType;

    private String alarmPower;

    private String alarmConvergeName;

    private String alarmConvergeId;

    private String remark;

    private Date updateTime;

}
