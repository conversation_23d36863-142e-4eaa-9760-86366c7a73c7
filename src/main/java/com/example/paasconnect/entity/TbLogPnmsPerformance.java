package com.example.paasconnect.entity;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @title: 实体类
 * @Description:
 * <AUTHOR>
 * @date 2022/3/23 18:31
 * @return
 */
@Table
@Data
public class TbLogPnmsPerformance {

    @Id
    @JSONField(serialize = false)
    private String id;

    @JSONField(serialize = false)
    private String kafkaType;

    @JSONField(serialize = false)
    private String unitId;

    @JSONField(name = "unitId")
    private String pnmsUnitId;

    private String extUnitId;

    private String kpiId;

    private String kpiValue;

    private String kpiDetail;

    private String kpiName;

    private Date cllTime;

    private String cllTimeStr;

    private String unitName;

    @JSONField(name = "interva")
    private String cllInterval;

}
