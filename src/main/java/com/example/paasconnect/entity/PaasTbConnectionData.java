package com.example.paasconnect.entity;


import lombok.Data;


import javax.persistence.Id;
import javax.persistence.Table;

/**
 * @title: 实体类
 * @Description:
 * <AUTHOR>
 * @date 2022/3/23 18:31
 * @return
 */
@Table
@Data
public class PaasTbConnectionData {

    @Id
    private String id;

    private String vip;

    private String servicePort;

    private String managePort;

    private String databaseName;

    private String businessName;

    private String date;

    private String connet;

    private String time;

    private String remark;


}
