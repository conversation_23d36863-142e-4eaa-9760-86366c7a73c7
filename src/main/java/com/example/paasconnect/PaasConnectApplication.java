package com.example.paasconnect;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
public class PaasConnectApplication {

    public static void main(String[] args) {
        SpringApplication.run(PaasConnectApplication.class, args);
        System.out.println("*******************************************\n" +
                "*                                         *\n" +
                "*******************************************\n" +
                "                                        @@@                    @@@            \n" +
                "                                       @.@@@@     启动成功     @@@*@            \n" +
                "                                       @..@@@@8     ...    @@@@@.@:           \n" +
                "                                       @....@@& ....@@o.....&@...#@           \n" +
                "                          :            @.............@...........8@           \n" +
                "                8@@@o:o@@@@@@@@        @......@...@..............@#           \n" +
                "              @@         @@@@@@@@      @@.........@...... @ .....@            \n" +
                "             @                          @......**.@*****:***...&@@            \n" +
                "            @                      o@@@&@@o......@......@.......@&            \n" +
                "            @                  @@@@::::::@ .....&.**..***:..... @             \n" +
                "            @              @@@::@@@o:::@@@@@@@....@@..#@... @@@@@@            \n" +
                "            @@          @@::@@:::::@:::@@@@@@@.....#@##.....@@@@@@            \n" +
                "             @.      @@@@:::::::::::::::::::@@..............#@                \n" +
                "              @#   @@::::o::::::::::::::::::::@*...........@                  \n" +
                "               @@@@@::::::::::::::::::::::::::@@..........@                   \n" +
                "                 @:::::::::::::::::::::::::::::@@........@                    \n" +
                "                 @:::::::::::::::::::::::::::::::@@@...@@                     \n" +
                "                 @::::::::::::::::::::::::::::::::@@@@@                       \n" +
                "                 @::::::::::::::::::::::::::::::::::@                         \n" +
                "                 @::::::::::::::::::::::::::::::::::@                         \n" +
                "                 @::::::::::::::::::::::::::::::::::@                         \n" +
                "                 @::::::::::::::::::::::::::::::::::@                         \n" +
                "                 @::::::::::::::::::::::::::::::::::@                         \n" +
                "                 @@::::::::::::::::::::::::::::::::@                          \n" +
                "                   @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@                          \n" +
                "                                                                              \n");
    }

}
