#!/bin/bash

# 编译脚本 - 使用特定Maven配置
cd "$(dirname "$0")/.." || exit 1
echo "开始编译项目..."

/Library/Java/JavaVirtualMachines/jdk1.8.0_271.jdk/Contents/Home/bin/java \
-Dmaven.home=/Users/<USER>/workspace/m2/apache-maven-3.2.3 \
-Dclassworlds.conf=/Users/<USER>/workspace/m2/apache-maven-3.2.3/bin/m2.conf \
-Dfile.encoding=UTF-8 \
-classpath /Users/<USER>/workspace/m2/apache-maven-3.2.3/boot/plexus-classworlds-2.5.1.jar \
org.codehaus.classworlds.Launcher \
-s /Users/<USER>/workspace/m2-bjunicom/apache-maven-3.2.3/conf/settings-sitechBJunicom.xml \
-DskipTests=true clean install

if [ $? -eq 0 ]; then
  echo "编译成功！JAR包位置: $(pwd)/target/yxjk-Alarm-0.0.1-SNAPSHOT.jar"
else
  echo "编译失败，请检查错误信息"
  exit 1
fi 