#!/bin/bash

# 运行脚本 - 启动编译后的应用程序
cd "$(dirname "$0")/.." || exit 1
echo "开始运行应用程序..."

# 检查JAR包是否存在
if [ ! -f "target/yxjk-Alarm-0.0.1-SNAPSHOT.jar" ]; then
  echo "JAR包不存在，请先运行 ./scripts/compile.sh 编译项目"
  exit 1
fi

# 启动应用程序
/Library/Java/JavaVirtualMachines/jdk1.8.0_271.jdk/Contents/Home/bin/java -jar target/yxjk-Alarm-0.0.1-SNAPSHOT.jar

# 如果需要指定配置文件，可以使用以下命令
# /Library/Java/JavaVirtualMachines/jdk1.8.0_271.jdk/Contents/Home/bin/java -jar target/yxjk-Alarm-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev 