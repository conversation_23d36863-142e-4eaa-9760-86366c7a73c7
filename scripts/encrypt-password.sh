#!/bin/bash

# 数据库密码加密工具脚本

# 检查参数
if [ "$#" -lt 1 ]; then
  echo "用法: $0 <明文密码> [密钥]"
  echo "如果不提供密钥，将使用默认密钥"
  exit 1
fi

# 获取参数
PLAIN_PASSWORD="$1"
ENCRYPTION_KEY="${2:-cloud}"  # 如果没有提供密钥，使用默认值

# 项目路径
PROJECT_DIR="$(cd "$(dirname "$0")/.." && pwd)"
CLASSPATH="$PROJECT_DIR/target/classes:$PROJECT_DIR/target/dependency/*"

# 运行加密工具
java -cp "$CLASSPATH" com.example.paasconnect.util.JasyptEncryptorUtil encrypt "$ENCRYPTION_KEY" "$PLAIN_PASSWORD"

echo ""
echo "配置示例:"
echo "spring:"
echo "  datasource:"
echo "    password: ENC(上面输出的加密结果)"
echo ""
echo "jasypt:"
echo "  encryptor:"
echo "    password: \${JASYPT_ENCRYPTOR_PASSWORD:$ENCRYPTION_KEY}"
echo ""
echo "注意: 在生产环境中，应该使用环境变量传递密钥，而不是硬编码在配置文件中"
echo "export JASYPT_ENCRYPTOR_PASSWORD=\"$ENCRYPTION_KEY\"" 